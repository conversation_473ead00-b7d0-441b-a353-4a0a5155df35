import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import random
import time
import json
from datetime import datetime
from itertools import product
import importlib.util
import sys
from collections import deque

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class AlgorithmPerformanceComparison:
    """算法性能对比实验类 - 复现论文6.2节内容"""
    
    def __init__(self, debug_max_consumers=50, reduced_time_periods=4, reduced_price_options=5):
        """
        初始化算法对比实验
        
        Args:
            debug_max_consumers: 消费者数量（控制问题规模）
            reduced_time_periods: 时间段数量（对应论文中的|S|）
            reduced_price_options: 价格选项数量
        """
        
        self.debug_max_consumers = debug_max_consumers
        self.reduced_time_periods = reduced_time_periods
        self.reduced_price_options = reduced_price_options
        
        # 实验参数（基于论文Table 4）
        self.max_iterations = 50  # 减少迭代数以快速看到效果
        self.time_limit_seconds = 150  # 2.5分钟时间限制
        self.tabu_list_size = 3
        self.ils_perturbation_strength = 2
        
        # 初始化OCDA评估器和相关组件
        self.setup_evaluation_framework()
        
        # 生成简化的问题实例
        self.setup_reduced_problem_instance()
        
        print(f"✓ 实验设置完成:")
        print(f"  消费者数量: {self.debug_max_consumers}")
        print(f"  时间段数量: {self.reduced_time_periods}")
        print(f"  价格选项数量: {self.reduced_price_options}")
        print(f"  最大迭代数: {self.max_iterations}")
        print(f"  时间限制: {self.time_limit_seconds}秒")
        
    def setup_evaluation_framework(self):
        """设置评估框架"""
        print("正在设置评估框架...")
        
        # 动态导入自适应机制与模拟退火模块
        try:
            spec = importlib.util.spec_from_file_location("halns_module", "自适应机制与模拟退火.py")
            halns_module = importlib.util.module_from_spec(spec)
            sys.modules["halns_module"] = halns_module
            spec.loader.exec_module(halns_module)
            
            self.OCDAEvaluator = halns_module.OCDAEvaluator
            self.DestroyRepairOperators = halns_module.DestroyRepairOperators
            
            print("✓ HALNS模块加载成功")
        except Exception as e:
            print(f"✗ HALNS模块加载失败: {e}")
            raise
        
        # 初始化评估器
        self.evaluator = self.OCDAEvaluator(debug_max_consumers=self.debug_max_consumers)
        
    def setup_reduced_problem_instance(self):
        """设置简化的问题实例"""
        print("正在生成简化问题实例...")
        
        # 简化时间段（选择前N个）
        original_periods = self.evaluator.time_periods
        self.time_periods = original_periods[:self.reduced_time_periods]
        
        # 简化价格集合
        original_prices = [2, 3, 4, 5, 6, 7, 8, 9]
        self.price_set = original_prices[:self.reduced_price_options]
        
        # 更新期间订单统计（只保留选定的时间段）
        self.period_orders = {period: self.evaluator.period_orders[period] 
                             for period in self.time_periods}
        
        print(f"✓ 问题实例设置完成:")
        print(f"  时间段: {self.time_periods}")
        print(f"  价格集合: {self.price_set}")
        print(f"  各时间段订单量: {self.period_orders}")
        
    def generate_random_solution(self):
        """生成随机定价解"""
        solution = {}
        for period in self.time_periods:
            solution[period] = random.choice(self.price_set)
        return solution
    
    def generate_greedy_initial_solution(self):
        """生成贪心初始解（高订单量时间段用高价格）"""
        solution = {}
        sorted_periods = sorted(self.period_orders.items(), key=lambda x: x[1], reverse=True)
        sorted_prices = sorted(self.price_set, reverse=True)
        
        for i, (period, _) in enumerate(sorted_periods):
            if i < len(sorted_prices):
                solution[period] = sorted_prices[i]
            else:
                solution[period] = sorted_prices[-1]
        
        return solution
    
    def evaluate_solution(self, solution):
        """评估定价方案，返回(利润, 收益, 成本)"""
        try:
            # 构建完整解（未选中的时间段使用默认价格）
            full_solution = {}
            for period in self.evaluator.time_periods:
                if period in solution:
                    full_solution[period] = solution[period]
                else:
                    full_solution[period] = 5  # 默认价格
            
            return self.evaluator.evaluate_pricing_scheme(full_solution)
        except Exception as e:
            return float('-inf'), 0, float('inf')
    
    def get_neighbors(self, solution):
        """获取解的邻域（单点变化）"""
        neighbors = []
        for period in solution:
            for price in self.price_set:
                if price != solution[period]:
                    neighbor = solution.copy()
                    neighbor[period] = price
                    neighbors.append(neighbor)
        return neighbors
    
    def solution_to_string(self, solution):
        """将解转换为字符串（用于禁忌列表）"""
        return str(sorted(solution.items()))
    
    def perturb_solution(self, solution, strength=2):
        """扰动解（用于ILS）"""
        perturbed = solution.copy()
        periods_to_change = random.sample(list(solution.keys()), 
                                         min(strength, len(solution)))
        
        for period in periods_to_change:
            perturbed[period] = random.choice(self.price_set)
        
        return perturbed
    
    def run_enumeration_method(self):
        """
        运行枚举方法（EM）
        枚举所有可能的定价组合并评估
        """
        
        start_time = time.time()
        
        # 生成所有可能的定价组合
        all_combinations = list(product(self.price_set, repeat=len(self.time_periods)))
        total_combinations = len(all_combinations)
        
        
        best_solution = None
        best_profit = float('-inf')
        best_revenue = 0
        best_cost = float('inf')
        
        evaluated_count = 0
        
        for i, price_combination in enumerate(all_combinations):
            # 检查时间限制
            if time.time() - start_time > self.time_limit_seconds:
                break
            
            # 构建解
            solution = {period: price for period, price in zip(self.time_periods, price_combination)}
            
            # 评估解
            profit, revenue, cost = self.evaluate_solution(solution)
            evaluated_count += 1
            
            if profit > best_profit:
                best_solution = solution.copy()
                best_profit = profit
                best_revenue = revenue
                best_cost = cost
            
            # 进度报告
            if (i + 1) % max(1, total_combinations // 10) == 0:
                elapsed = time.time() - start_time
                progress = (i + 1) / total_combinations * 100
        
        total_time = time.time() - start_time
        
        result = {
            'algorithm': 'EM',
            'best_solution': best_solution,
            'best_profit': best_profit,
            'best_revenue': best_revenue,
            'best_cost': best_cost,
            'total_time': total_time,
            'evaluations': evaluated_count,
            'total_combinations': total_combinations,
            'completion_rate': evaluated_count / total_combinations * 100,
            'avg_time_per_evaluation': total_time / evaluated_count if evaluated_count > 0 else 0
        }
        
        print(f"\nEM算法完成:")
        print(f"  最佳利润: {best_profit:.2f}")
        print(f"  总时间: {total_time:.1f}秒")
        print(f"  评估数量: {evaluated_count}/{total_combinations}")
        print(f"  完成率: {result['completion_rate']:.1f}%")
        
        return result
    
    def run_tabu_search(self):
        """
        运行禁忌搜索（TS）
        基于论文参数设置：禁忌列表长度=3，最大迭代数=150（这里减少到50）
        """
        
        start_time = time.time()
        
        # 初始化
        current_solution = self.generate_greedy_initial_solution()
        current_profit, current_revenue, current_cost = self.evaluate_solution(current_solution)
        
        best_solution = current_solution.copy()
        best_profit = current_profit
        best_revenue = current_revenue
        best_cost = current_cost
        
        # 禁忌列表
        tabu_list = deque(maxlen=self.tabu_list_size)
        
        iteration = 0
        evaluations = 1  # 初始解评估
        no_improvement_count = 0
        
        
        while iteration < self.max_iterations and time.time() - start_time < self.time_limit_seconds:
            iteration += 1
            
            # 生成邻域
            neighbors = self.get_neighbors(current_solution)
            
            best_neighbor = None
            best_neighbor_profit = float('-inf')
            best_neighbor_revenue = 0
            best_neighbor_cost = float('inf')
            
            # 评估邻域中的解
            for neighbor in neighbors:
                neighbor_str = self.solution_to_string(neighbor)
                
                # 检查是否在禁忌列表中
                if neighbor_str in tabu_list:
                    continue
                
                profit, revenue, cost = self.evaluate_solution(neighbor)
                evaluations += 1
                
                if profit > best_neighbor_profit:
                    best_neighbor = neighbor
                    best_neighbor_profit = profit
                    best_neighbor_revenue = revenue
                    best_neighbor_cost = cost
            
            # 如果没有找到非禁忌的邻居，选择禁忌列表中最好的（渴望准则）
            if best_neighbor is None:
                for neighbor in neighbors:
                    profit, revenue, cost = self.evaluate_solution(neighbor)
                    evaluations += 1
                    
                    if profit > best_neighbor_profit:
                        best_neighbor = neighbor
                        best_neighbor_profit = profit
                        best_neighbor_revenue = revenue
                        best_neighbor_cost = cost
            
            if best_neighbor is not None:
                # 更新当前解
                current_solution = best_neighbor
                current_profit = best_neighbor_profit
                current_revenue = best_neighbor_revenue
                current_cost = best_neighbor_cost
                
                # 添加到禁忌列表
                tabu_list.append(self.solution_to_string(current_solution))
                
                # 更新最佳解
                if current_profit > best_profit:
                    best_solution = current_solution.copy()
                    best_profit = current_profit
                    best_revenue = current_revenue
                    best_cost = current_cost
                    no_improvement_count = 0
                else:
                    no_improvement_count += 1
                
                # 进度报告
                if iteration % 10 == 0:
                    elapsed = time.time() - start_time
            else:
                break
        
        total_time = time.time() - start_time
        
        result = {
            'algorithm': 'TS',
            'best_solution': best_solution,
            'best_profit': best_profit,
            'best_revenue': best_revenue,
            'best_cost': best_cost,
            'total_time': total_time,
            'iterations': iteration,
            'evaluations': evaluations,
            'no_improvement_iterations': no_improvement_count,
            'avg_time_per_iteration': total_time / iteration if iteration > 0 else 0
        }
        
        print(f"\nTS算法完成:")
        print(f"  最佳利润: {best_profit:.2f}")
        print(f"  总时间: {total_time:.1f}秒")
        print(f"  迭代数: {iteration}")
        print(f"  评估数: {evaluations}")
        
        return result
    
    def local_search(self, initial_solution):
        """局部搜索（爬山法）"""
        current_solution = initial_solution.copy()
        current_profit, current_revenue, current_cost = self.evaluate_solution(current_solution)
        
        improved = True
        iterations = 0
        
        while improved and iterations < 20:  # 限制局部搜索迭代数
            improved = False
            iterations += 1
            
            neighbors = self.get_neighbors(current_solution)
            
            for neighbor in neighbors:
                profit, revenue, cost = self.evaluate_solution(neighbor)
                
                if profit > current_profit:
                    current_solution = neighbor
                    current_profit = profit
                    current_revenue = revenue
                    current_cost = cost
                    improved = True
                    break
        
        return current_solution, current_profit, current_revenue, current_cost, iterations
    
    def run_iterated_local_search(self):
        """
        运行迭代局部搜索（ILS）
        基于论文设置，实现扰动+局部搜索的迭代过程
        """
        
        start_time = time.time()
        
        # 初始化
        initial_solution = self.generate_greedy_initial_solution()
        current_solution, current_profit, current_revenue, current_cost, ls_iters = self.local_search(initial_solution)
        
        best_solution = current_solution.copy()
        best_profit = current_profit
        best_revenue = current_revenue
        best_cost = current_cost
        
        iteration = 0
        total_evaluations = ls_iters
        no_improvement_count = 0
        
        
        while iteration < self.max_iterations and time.time() - start_time < self.time_limit_seconds:
            iteration += 1
            
            # 扰动当前解
            perturbed_solution = self.perturb_solution(current_solution, self.ils_perturbation_strength)
            
            # 局部搜索
            ls_solution, ls_profit, ls_revenue, ls_cost, ls_iters = self.local_search(perturbed_solution)
            total_evaluations += ls_iters
            
            # 接受准则（接受更好的解）
            if ls_profit > current_profit:
                current_solution = ls_solution
                current_profit = ls_profit
                current_revenue = ls_revenue
                current_cost = ls_cost
                
                # 更新最佳解
                if ls_profit > best_profit:
                    best_solution = ls_solution.copy()
                    best_profit = ls_profit
                    best_revenue = ls_revenue
                    best_cost = ls_cost
                    no_improvement_count = 0
                else:
                    no_improvement_count += 1
            else:
                no_improvement_count += 1
            

        
        total_time = time.time() - start_time
        
        result = {
            'algorithm': 'ILS',
            'best_solution': best_solution,
            'best_profit': best_profit,
            'best_revenue': best_revenue,
            'best_cost': best_cost,
            'total_time': total_time,
            'iterations': iteration,
            'evaluations': total_evaluations,
            'no_improvement_iterations': no_improvement_count,
            'avg_time_per_iteration': total_time / iteration if iteration > 0 else 0
        }
        
        print(f"\nILS算法完成:")
        print(f"  最佳利润: {best_profit:.2f}")
        print(f"  总时间: {total_time:.1f}秒")
        print(f"  迭代数: {iteration}")
        print(f"  评估数: {total_evaluations}")
        
        return result
    
    def run_halns(self):
        """
        运行HALNS算法
        使用现有的实现，但适配到简化问题
        """
        
        start_time = time.time()
        
        # 创建适配的HALNS实例
        halns_operators = self.DestroyRepairOperators(debug_max_consumers=self.debug_max_consumers)
        
        # 修改HALNS的参数以适应简化问题
        halns_operators.evaluator.time_periods = self.time_periods
        halns_operators.evaluator.period_orders = self.period_orders
        halns_operators.price_set = self.price_set
        
        # 运行HALNS
        best_solution, best_profit = halns_operators.run_halns(max_iterations=self.max_iterations)
        
        total_time = time.time() - start_time
        
        if best_solution is not None:
            # 只保留简化问题相关的时间段
            filtered_solution = {period: price for period, price in best_solution.items() 
                               if period in self.time_periods}
            
            # 重新评估确保一致性
            profit, revenue, cost = self.evaluate_solution(filtered_solution)
            
            result = {
                'algorithm': 'HALNS',
                'best_solution': filtered_solution,
                'best_profit': profit,
                'best_revenue': revenue,
                'best_cost': cost,
                'total_time': total_time,
                'iterations': self.max_iterations,
                'evaluations': -1,  # HALNS内部统计复杂，设为-1
                'halns_reported_profit': best_profit
            }
        else:
            result = {
                'algorithm': 'HALNS',
                'best_solution': None,
                'best_profit': float('-inf'),
                'best_revenue': 0,
                'best_cost': float('inf'),
                'total_time': total_time,
                'iterations': 0,
                'evaluations': 0,
                'halns_reported_profit': float('-inf')
            }
        
        print(f"\nHALNS算法完成:")
        print(f"  最佳利润: {result['best_profit']:.2f}")
        print(f"  总时间: {total_time:.1f}秒")
        
        return result
    
    def run_comparison_experiment(self, num_runs=3):
        """
        运行完整的算法对比实验
        基于论文6.2节的实验设计
        """
        print("="*100)
        print("开始算法性能对比实验（复现论文6.2节）")
        print("="*100)
        print(f"实验设置:")
        print(f"  问题规模: {self.reduced_time_periods}个时间段, {self.reduced_price_options}个价格选项")
        print(f"  消费者数量: {self.debug_max_consumers}")
        print(f"  运行次数: {num_runs}")
        print(f"  时间限制: {self.time_limit_seconds}秒")
        print(f"  最大迭代数: {self.max_iterations}")
        
        # 存储所有结果
        all_results = []
        
        algorithms = [
            ('EM', self.run_enumeration_method),
            ('TS', self.run_tabu_search),
            ('ILS', self.run_iterated_local_search),
            ('HALNS', self.run_halns)
        ]
        
        # 运行每个算法多次
        for run in range(num_runs):
            print(f"\n{'='*80}")
            print(f"第 {run + 1}/{num_runs} 次运行")
            print(f"{'='*80}")
            
            run_results = []
            
            for alg_name, alg_func in algorithms:
                print(f"\n开始运行 {alg_name} 算法...")
                
                try:
                    # 设置随机种子确保可重现性
                    random.seed(42 + run)
                    np.random.seed(42 + run)
                    
                    result = alg_func()
                    result['run'] = run + 1
                    run_results.append(result)
                    
                except Exception as e:
                    print(f"{alg_name} 算法运行失败: {e}")
                    # 添加失败结果
                    fail_result = {
                        'algorithm': alg_name,
                        'run': run + 1,
                        'best_profit': float('-inf'),
                        'total_time': self.time_limit_seconds,
                        'status': 'failed',
                        'error': str(e)
                    }
                    run_results.append(fail_result)
                
                # 短暂休息避免系统过载
                time.sleep(1)
            
            all_results.extend(run_results)
        
        # 分析和展示结果
        self.analyze_and_display_results(all_results, num_runs)
        
        # 保存结果
        self.save_comparison_results(all_results, num_runs)
        
        return all_results
    
    def analyze_and_display_results(self, all_results, num_runs):
        """分析和展示对比结果"""
        print(f"\n{'='*100}")
        print("算法性能对比分析")
        print(f"{'='*100}")
        
        # 按算法分组统计
        algorithm_stats = {}
        
        for result in all_results:
            alg = result['algorithm']
            if alg not in algorithm_stats:
                algorithm_stats[alg] = {
                    'profits': [],
                    'times': [],
                    'runs': [],
                    'successful_runs': 0
                }
            
            if result.get('status') != 'failed':
                algorithm_stats[alg]['profits'].append(result['best_profit'])
                algorithm_stats[alg]['times'].append(result['total_time'])
                algorithm_stats[alg]['successful_runs'] += 1
            
            algorithm_stats[alg]['runs'].append(result)
        
        # 计算统计指标
        print(f"{'算法':<10} {'平均利润':<12} {'最佳利润':<12} {'平均时间':<12} {'成功率':<10}")
        print("-" * 70)
        
        for alg in ['EM', 'TS', 'ILS', 'HALNS']:
            if alg in algorithm_stats:
                stats = algorithm_stats[alg]
                
                if stats['profits']:
                    avg_profit = np.mean(stats['profits'])
                    best_profit = np.max(stats['profits'])
                    avg_time = np.mean(stats['times'])
                    success_rate = stats['successful_runs'] / num_runs * 100
                    
                    print(f"{alg:<10} {avg_profit:<12.2f} {best_profit:<12.2f} "
                          f"{avg_time:<12.1f} {success_rate:<10.1f}%")
                else:
                    print(f"{alg:<10} {'失败':<12} {'失败':<12} {'失败':<12} {'0.0%':<10}")
        
        # 找出最佳算法（优先考虑利润，利润相同时选择耗时最少的）
        best_algorithm = None
        best_avg_profit = float('-inf')
        best_avg_time = float('inf')
        
        for alg, stats in algorithm_stats.items():
            if stats['profits']:
                avg_profit = np.mean(stats['profits'])
                avg_time = np.mean(stats['times'])
                
                # 如果利润更高，或者利润相同但时间更短，则更新最佳算法
                if (avg_profit > best_avg_profit or 
                    (abs(avg_profit - best_avg_profit) < 1e-6 and avg_time < best_avg_time)):
                    best_avg_profit = avg_profit
                    best_avg_time = avg_time
                    best_algorithm = alg
        
        print(f"\n最佳算法: {best_algorithm} (平均利润: {best_avg_profit:.2f}, 平均时间: {best_avg_time:.1f}秒)")
        
        # 详细对比分析
        print(f"\n详细性能分析:")
        
        if 'HALNS' in algorithm_stats and algorithm_stats['HALNS']['profits']:
            halns_avg = np.mean(algorithm_stats['HALNS']['profits'])
            halns_time = np.mean(algorithm_stats['HALNS']['times'])
            
            print(f"HALNS表现:")
            print(f"  平均利润: {halns_avg:.2f}")
            print(f"  平均时间: {halns_time:.1f}秒")
            
            for alg in ['EM', 'TS', 'ILS']:
                if alg in algorithm_stats and algorithm_stats[alg]['profits']:
                    other_avg = np.mean(algorithm_stats[alg]['profits'])
                    other_time = np.mean(algorithm_stats[alg]['times'])
                    
                    profit_improvement = ((halns_avg - other_avg) / abs(other_avg) * 100) if other_avg != 0 else 0
                    time_ratio = halns_time / other_time if other_time > 0 else 0
                    
                    print(f"vs {alg}:")
                    print(f"  利润改善: {profit_improvement:+.1f}%")
                    print(f"  时间比率: {time_ratio:.2f}x")
        
        # 生成可视化图表
        self.create_performance_charts(algorithm_stats)
    
    def create_performance_charts(self, algorithm_stats):
        """创建性能对比图表"""
        print(f"\n生成性能对比图表...")
        
        # 准备数据
        algorithms = []
        avg_profits = []
        avg_times = []
        
        for alg in ['EM', 'TS', 'ILS', 'HALNS']:
            if alg in algorithm_stats and algorithm_stats[alg]['profits']:
                algorithms.append(alg)
                avg_profits.append(np.mean(algorithm_stats[alg]['profits']))
                avg_times.append(np.mean(algorithm_stats[alg]['times']))
        
        if len(algorithms) < 2:
            print("数据不足，无法生成图表")
            return
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 利润对比图
        bars1 = ax1.bar(algorithms, avg_profits, color=['skyblue', 'lightgreen', 'orange', 'red'])
        ax1.set_title('算法平均利润对比')
        ax1.set_ylabel('平均利润 (RMB)')
        ax1.set_xlabel('算法')
        
        # 在柱子上添加数值标签
        for bar, profit in zip(bars1, avg_profits):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{profit:.1f}', ha='center', va='bottom')
        
        # 时间对比图
        bars2 = ax2.bar(algorithms, avg_times, color=['skyblue', 'lightgreen', 'orange', 'red'])
        ax2.set_title('算法平均运行时间对比')
        ax2.set_ylabel('平均时间 (秒)')
        ax2.set_xlabel('算法')
        
        # 在柱子上添加数值标签
        for bar, time in zip(bars2, avg_times):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{time:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('algorithm_performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✓ 图表已保存为: algorithm_performance_comparison.png")
    
    def save_comparison_results(self, all_results, num_runs):
        """保存对比实验结果"""
        print(f"\n保存实验结果...")
        
        # 准备保存的数据
        save_data = {
            'experiment_info': {
                'title': 'Algorithm Performance Comparison - 复现论文6.2节',
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'problem_settings': {
                    'time_periods': self.reduced_time_periods,
                    'price_options': self.reduced_price_options,
                    'consumers': self.debug_max_consumers,
                    'time_limit_seconds': self.time_limit_seconds,
                    'max_iterations': self.max_iterations
                },
                'paper_reference': {
                    'section': '6.2 Performance of the algorithm',
                    'table_reference': 'Table 5 and Table 6',
                    'algorithms_compared': ['EM', 'TS', 'ILS', 'HALNS']
                }
            },
            'experiment_results': {
                'num_runs': num_runs,
                'detailed_results': all_results
            },
            'algorithm_parameters': {
                'TS': {
                    'tabu_list_size': self.tabu_list_size,
                    'max_iterations': self.max_iterations
                },
                'ILS': {
                    'perturbation_strength': self.ils_perturbation_strength,
                    'max_iterations': self.max_iterations
                },
                'HALNS': {
                    'max_iterations': self.max_iterations,
                    'note': 'Using existing implementation with adapted parameters'
                }
            }
        }
        
        # 计算汇总统计
        algorithm_stats = {}
        for result in all_results:
            alg = result['algorithm']
            if alg not in algorithm_stats:
                algorithm_stats[alg] = {'profits': [], 'times': [], 'success_count': 0}
            
            if result.get('status') != 'failed':
                algorithm_stats[alg]['profits'].append(result['best_profit'])
                algorithm_stats[alg]['times'].append(result['total_time'])
                algorithm_stats[alg]['success_count'] += 1
        
        summary_stats = {}
        for alg, stats in algorithm_stats.items():
            if stats['profits']:
                summary_stats[alg] = {
                    'average_profit': float(np.mean(stats['profits'])),
                    'best_profit': float(np.max(stats['profits'])),
                    'worst_profit': float(np.min(stats['profits'])),
                    'std_profit': float(np.std(stats['profits'])),
                    'average_time': float(np.mean(stats['times'])),
                    'total_runs': num_runs,
                    'successful_runs': stats['success_count'],
                    'success_rate': stats['success_count'] / num_runs * 100
                }
            else:
                summary_stats[alg] = {
                    'average_profit': None,
                    'success_rate': 0,
                    'note': 'All runs failed'
                }
        
        save_data['summary_statistics'] = summary_stats
        
        # 保存到JSON文件
        filename = f'algorithm_comparison_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 详细结果已保存为: {filename}")
        
        # 另外保存一个简化的CSV文件便于查看
        csv_data = []
        for result in all_results:
            csv_data.append({
                'Algorithm': result['algorithm'],
                'Run': result.get('run', 1),
                'Best_Profit': result.get('best_profit', None),
                'Total_Time': result.get('total_time', None),
                'Status': result.get('status', 'success')
            })
        
        csv_filename = f'algorithm_comparison_summary_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        pd.DataFrame(csv_data).to_csv(csv_filename, index=False)
        print(f"✓ 汇总表格已保存为: {csv_filename}")


def main():
    """主函数 - 运行完整的算法对比实验"""
    
    # 设置随机种子确保可重现性
    random.seed(42)
    np.random.seed(42)
    
    try:
        # 创建对比实验实例
        # 可以调整这些参数来控制问题规模和运行时间
        comparison = AlgorithmPerformanceComparison(
            debug_max_consumers=30,      # 消费者数量
            reduced_time_periods=4,      # 时间段数量（对应论文中的|S|）
            reduced_price_options=5      # 价格选项数量
        )
        
        # 运行对比实验
        results = comparison.run_comparison_experiment(num_runs=1)  # 运行2次求平均
        
        
    except Exception as e:
        print(f"实验执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()