import math
import copy
from typing import List, Dict, Tuple, Any, Set
import numpy as np

class OrderGroup:
    """订单组类，包含订单序列和相关信息"""
    def __init__(self, orders: List[int], sequence: List[int], starting_order: int):
        self.orders = orders  # 订单ID列表
        self.sequence = sequence  # 访问序列（包含取餐点和送餐点）
        self.starting_order = starting_order  # 起始订单ID
        self.virtual_driver_id = None  # 虚拟司机ID
        self.estimated_cost = 0.0  # 预估成本
        self.dispatch_cost = 0.0  # 调度成本
        
    def __len__(self):
        return len(self.orders)
        
    def contains_order(self, order_id: int) -> bool:
        return order_id in self.orders
    
    def get_orders_set(self) -> Set[int]:
        """返回订单集合"""
        return set(self.orders)

class OGGM:
    """订单组生成方法 (Order Group Generation Method) - 修正版"""
    
    def __init__(self, food_delivery_optimizer):
        """
        初始化OGGM
        
        Args:
            food_delivery_optimizer: FoodDeliveryOptimizer实例，提供所有必要参数
        """
        self.optimizer = food_delivery_optimizer
        
        # 从优化器获取参数（确保与Gurobi.py一致）
        self.commission_rate = food_delivery_optimizer.commission_rate
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        
        # 空间和订单信息
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_values = food_delivery_optimizer.order_values
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        
        # OGGM特定参数 - 修正
        self.delta = 1  # 虚拟司机计算时考虑的最近司机数量
        self.delay_threshold = 0.3  # 延迟度阈值μ（调整为更合理的值）
        
        print("OGGM初始化完成 - 修正版")
        print(f"参数设置 - 旅行成本: {self.travel_cost_per_unit}, 延迟惩罚: {self.penalty_cost_per_unit}")
        print(f"最大订单数: {self.max_orders_per_driver}, 延迟阈值: {self.delay_threshold}")
    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """
        计算两点间的欧几里得距离（与Gurobi.py一致）
        """
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def get_order_coordinates(self, order_id: int) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """获取订单的餐厅和顾客坐标"""
        restaurant_id = self.order_restaurants[order_id]
        restaurant_coord = self.restaurant_coords[restaurant_id]
        customer_coord = self.customer_coords[order_id]
        return restaurant_coord, customer_coord
    
    def calculate_single_order_distance(self, order_id: int, driver_start: Tuple[float, float]) -> float:
        """计算单独配送一个订单的总距离：起点→餐厅→顾客"""
        restaurant_coord, customer_coord = self.get_order_coordinates(order_id)
        
        dist_to_restaurant = self.calculate_distance(driver_start, restaurant_coord)
        dist_restaurant_to_customer = self.calculate_distance(restaurant_coord, customer_coord)
        
        return dist_to_restaurant + dist_restaurant_to_customer
    
    def calculate_two_orders_combined_distance(self, order1_id: int, order2_id: int, 
                                            driver_start: Tuple[float, float]) -> Tuple[float, float]:
        """
        计算两个订单合并配送的最短距离（按照附录J的公式J.1和J.2）
        修正版：确保正确计算所有可能路径
        """
        rest1_coord, cust1_coord = self.get_order_coordinates(order1_id)
        rest2_coord, cust2_coord = self.get_order_coordinates(order2_id)
        
        # 机制1：固定order1的餐厅为第一个访问节点
        dist_start_to_r1 = self.calculate_distance(driver_start, rest1_coord)
        
        # 三种可能的序列（对应Figure 10）
        sequences_order1_first = [
            # 序列1: 起点→r1→r2→c2→c1  
            dist_start_to_r1 + 
            self.calculate_distance(rest1_coord, rest2_coord) +
            self.calculate_distance(rest2_coord, cust2_coord) +
            self.calculate_distance(cust2_coord, cust1_coord),
            
            # 序列2: 起点→r1→r2→c1→c2
            dist_start_to_r1 + 
            self.calculate_distance(rest1_coord, rest2_coord) +
            self.calculate_distance(rest2_coord, cust1_coord) +
            self.calculate_distance(cust1_coord, cust2_coord),
            
            # 序列3: 起点→r1→c1→r2→c2
            dist_start_to_r1 + 
            self.calculate_distance(rest1_coord, cust1_coord) +
            self.calculate_distance(cust1_coord, rest2_coord) +
            self.calculate_distance(rest2_coord, cust2_coord)
        ]
        
        dist_order1_first = min(sequences_order1_first)
        
        # 机制2：固定order2的餐厅为第一个访问节点
        dist_start_to_r2 = self.calculate_distance(driver_start, rest2_coord)
        
        sequences_order2_first = [
            # 序列1: 起点→r2→r1→c1→c2
            dist_start_to_r2 + 
            self.calculate_distance(rest2_coord, rest1_coord) +
            self.calculate_distance(rest1_coord, cust1_coord) +
            self.calculate_distance(cust1_coord, cust2_coord),
            
            # 序列2: 起点→r2→r1→c2→c1
            dist_start_to_r2 + 
            self.calculate_distance(rest2_coord, rest1_coord) +
            self.calculate_distance(rest1_coord, cust2_coord) +
            self.calculate_distance(cust2_coord, cust1_coord),
            
            # 序列3: 起点→r2→c2→r1→c1
            dist_start_to_r2 + 
            self.calculate_distance(rest2_coord, cust2_coord) +
            self.calculate_distance(cust2_coord, rest1_coord) +
            self.calculate_distance(rest1_coord, cust1_coord)
        ]
        
        dist_order2_first = min(sequences_order2_first)
        
        return dist_order1_first, dist_order2_first
    
    def calculate_order_matching_degree(self, order1_id: int, order2_id: int, 
                                      virtual_driver1_pos: Tuple[float, float],
                                      virtual_driver2_pos: Tuple[float, float]) -> Tuple[float, float]:
        """
        计算两个订单的匹配度（按照附录J公式J.3）
        修正版：确保计算逻辑正确
        """
        # 分别配送的总距离
        dist1_separate = self.calculate_single_order_distance(order1_id, virtual_driver1_pos)
        dist2_separate = self.calculate_single_order_distance(order2_id, virtual_driver2_pos)
        total_separate_distance = dist1_separate + dist2_separate
        
        # 合并配送的距离 - 使用order1的虚拟司机位置
        dist_order1_first, dist_order2_first = self.calculate_two_orders_combined_distance(
            order1_id, order2_id, virtual_driver1_pos)
        
        # 计算匹配度（距离节省）
        fit_o1_o2 = total_separate_distance - dist_order1_first
        fit_o2_o1 = total_separate_distance - dist_order2_first
        
        return fit_o1_o2, fit_o2_o1
    
    def cheapest_insertion_single_order(self, sequence: List[str], new_order_id: int, 
                                      current_start_pos: Tuple[float, float]) -> Tuple[List[str], float]:
        """
        使用最便宜插入法将单个订单插入到现有序列中
        修正版：正确处理起始位置
        """
        if not sequence:
            # 空序列，直接创建新序列
            new_sequence = [f"pickup_{new_order_id}", f"delivery_{new_order_id}"]
            return new_sequence, 0.0
        
        best_sequence = None
        best_cost = float('inf')
        
        # 获取新订单的取餐点和送餐点
        pickup_node = f"pickup_{new_order_id}"
        delivery_node = f"delivery_{new_order_id}"
        
        # 尝试所有可能的插入位置
        for pickup_pos in range(len(sequence) + 1):
            for delivery_pos in range(pickup_pos + 1, len(sequence) + 2):
                # 创建新序列
                temp_sequence = sequence.copy()
                temp_sequence.insert(pickup_pos, pickup_node)
                temp_sequence.insert(delivery_pos, delivery_node)
                
                # 计算插入成本
                insertion_cost = self.calculate_sequence_distance_increase(
                    sequence, temp_sequence, current_start_pos)
                
                if insertion_cost < best_cost:
                    best_cost = insertion_cost
                    best_sequence = temp_sequence
        
        return best_sequence, best_cost
    
    def calculate_sequence_distance_increase(self, old_sequence: List[str], 
                                           new_sequence: List[str], 
                                           start_pos: Tuple[float, float]) -> float:
        """计算序列变化导致的距离增加"""
        old_distance = self.calculate_sequence_total_distance(old_sequence, start_pos)
        new_distance = self.calculate_sequence_total_distance(new_sequence, start_pos)
        return new_distance - old_distance
    
    def calculate_sequence_total_distance(self, sequence: List[str], 
                                        start_pos: Tuple[float, float]) -> float:
        """
        计算序列的总配送距离
        修正版：正确处理起始位置
        """
        if len(sequence) <= 1:
            return 0.0
        
        total_distance = 0.0
        current_pos = start_pos
        
        for node in sequence:
            # 解析节点信息
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id]
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            else:
                continue
            
            # 累加距离
            total_distance += self.calculate_distance(current_pos, next_pos)
            current_pos = next_pos
        
        return total_distance
    
    def estimate_sequence_delivery_times(self, sequence: List[str], start_time: float, 
                                       start_pos: Tuple[float, float]) -> Dict[int, float]:
        """
        估算序列中各订单的送达时间
        修正版：正确处理时间和位置
        """
        delivery_times = {}
        current_time = start_time
        current_pos = start_pos
        
        for i, node in enumerate(sequence):
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id] 
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            else:
                continue
            
            # 计算旅行时间并更新位置和时间
            travel_distance = self.calculate_distance(current_pos, next_pos)
            travel_time = travel_distance / self.driver_speed
            current_time += travel_time
            current_pos = next_pos
            
            # 如果是送餐节点，记录送达时间
            if node.startswith("delivery_"):
                delivery_times[order_id] = current_time
        
        return delivery_times
    
    def calculate_order_delay_degree(self, order_id: int, actual_delivery_time: float, 
                                   slot_start_time: float) -> float:
        """
        计算订单延迟度
        修正版：正确实现延迟度计算逻辑
        """
        # 目标配送时间 = 时段开始时间 + 预期配送时长
        target_delivery_time = slot_start_time + self.estimated_delivery_duration[order_id]
        
        # 延迟时间（如果实际时间超过目标时间）
        delay_time = max(actual_delivery_time - target_delivery_time, 0)
        
        # 延迟度 = 延迟时间 / 预期配送时长
        if self.estimated_delivery_duration[order_id] > 0:
            delay_degree = delay_time / self.estimated_delivery_duration[order_id]
        else:
            delay_degree = 0.0
        
        return delay_degree
    
    def calculate_max_delay_degree_in_sequence(self, sequence: List[str], start_time: float, 
                                             slot_start_time: float, 
                                             start_pos: Tuple[float, float]) -> float:
        """
        计算序列中所有订单的最大延迟度
        修正版：正确处理时间计算
        """
        delivery_times = self.estimate_sequence_delivery_times(sequence, start_time, start_pos)
        
        max_delay_degree = 0.0
        for order_id, delivery_time in delivery_times.items():
            delay_degree = self.calculate_order_delay_degree(order_id, delivery_time, slot_start_time)
            max_delay_degree = max(max_delay_degree, delay_degree)
        
        return max_delay_degree
    
    def get_virtual_driver_info(self, order_id: int, slot: int, 
                              actual_start_pos: Tuple[float, float] = None,
                              actual_start_time: float = None) -> Tuple[Tuple[float, float], float]:
        """
        为订单确定虚拟司机信息
        修正版：允许传入实际的司机起始位置和时间
        """
        # 如果未提供实际起始状态，则使用默认（通常是时段开始时或原始起点）
        virtual_position = actual_start_pos if actual_start_pos is not None else self.driver_start_coords[0]
        
        # 虚拟司机开始时间：如果提供了实际开始时间，则使用它，否则使用时段的名义开始时间
        nominal_slot_start_time = (slot - 1) * self.slot_duration
        virtual_start_time = actual_start_time if actual_start_time is not None else nominal_slot_start_time
        
        return virtual_position, virtual_start_time
    
    def generate_order_groups_for_slot(self, orders_in_slot: List[int], slot: int,
                                     actual_driver_start_pos: Tuple[float, float] = None,
                                     actual_driver_start_time: float = None) -> List[OrderGroup]:
        """
        为指定时段生成所有可能的订单组
        修正版：使用实际的司机起始位置和时间
        """
        print(f"\n=== 开始为时段{slot}生成订单组（修正版）===")
        print(f"时段{slot}订单: {orders_in_slot}")
        if actual_driver_start_pos and actual_driver_start_time is not None:
            print(f"使用实际司机起点: Pos={actual_driver_start_pos}, Time={actual_driver_start_time:.2f}")

        if not orders_in_slot:
            return []
        
        all_order_groups = []
        # 使用实际的或名义上的时段开始时间进行延迟度计算
        delay_calc_slot_start_time = actual_driver_start_time if actual_driver_start_time is not None else (slot - 1) * self.slot_duration


        print(f"时段{slot}用于延迟计算的开始时间: {delay_calc_slot_start_time:.2f}")
        
        # Step 1 & 2: 为每个订单初始化并获取虚拟司机信息
        
        base_start_pos, base_start_time = self.get_virtual_driver_info(
            orders_in_slot[0], slot, actual_driver_start_pos, actual_driver_start_time
        )


        # Step 3: 计算所有订单对的匹配度矩阵
        # 匹配度计算应使用统一的起始点（即实际司机起点）
        matching_matrix = {}
        for order1 in orders_in_slot:
            for order2 in orders_in_slot:
                if order1 != order2:
                    # calculate_order_matching_degree 使用的 virtual_driver_pos 应该是实际的司机起始位置
                    # 这里假设两个订单的匹配度计算基于同一个起点（实际司机起点）
                    fit_12, fit_21 = self.calculate_order_matching_degree(order1, order2, base_start_pos, base_start_pos)
                    matching_matrix[(order1, order2)] = fit_12
                    matching_matrix[(order2, order1)] = fit_21
        
        # Step 4-8: 对每个订单作为起始订单进行扩展
        for starting_order in orders_in_slot:
            # 起始订单的序列和扩展，都应该基于实际的司机起始位置和时间
            # start_pos, start_time = virtual_driver_info[starting_order] # OLD
            current_actual_start_pos = base_start_pos
            current_actual_start_time = base_start_time
            
            # 初始化
            current_order_cluster = {starting_order}
            current_sequence = [f"pickup_{starting_order}", f"delivery_{starting_order}"]
            remaining_orders = set(orders_in_slot) - {starting_order}
            
            # 创建初始订单组（只包含起始订单）
            initial_og = OrderGroup([starting_order], current_sequence.copy(), starting_order)
            all_order_groups.append(initial_og)
            
            # 尝试扩展订单组
            while remaining_orders and len(current_order_cluster) < self.max_orders_per_driver:
                # Step 5: 计算剩余订单与当前订单组的匹配度 (已在 matching_matrix 中基于 base_start_pos 计算)
                order_matching_scores = []
                for order_id in remaining_orders:
                    total_fit = 0.0
                    count = 0
                    for existing_order in current_order_cluster:
                        # 使用预先计算的基于共同起点的匹配度
                        fit_key = (existing_order, order_id) # 或者 (order_id, existing_order)
                        if (existing_order, order_id) in matching_matrix:
                             total_fit += matching_matrix[(existing_order, order_id)]
                             count +=1
                        if (order_id, existing_order) in matching_matrix: # 也考虑反向
                             total_fit += matching_matrix[(order_id, existing_order)]
                             count +=1

                    avg_fit = total_fit / count if count > 0 else 0.0
                    order_matching_scores.append((order_id, avg_fit))
                
                order_matching_scores.sort(key=lambda x: x[1], reverse=True)
                
                if not order_matching_scores:
                    break
                
                best_order, best_fit = order_matching_scores[0]
                
                if best_fit <= 0 and len(current_order_cluster) > 0 : #如果只有一个订单，匹配度可以是0
                    # print(f"    匹配度 {best_fit:.3f} <= 0，停止扩展订单组 {current_order_cluster}")
                    break
                
                # Step 8: 使用最便宜插入法将订单加入序列
                # 插入和延迟计算都使用实际的司机起始位置和时间
                new_sequence, insertion_cost = self.cheapest_insertion_single_order(
                    current_sequence, best_order, current_actual_start_pos)
                
                max_delay_degree = self.calculate_max_delay_degree_in_sequence(
                    new_sequence, current_actual_start_time, delay_calc_slot_start_time, current_actual_start_pos)
                
                if max_delay_degree > self.delay_threshold:
                    # print(f"    订单 {best_order} 加入导致最大延迟 {max_delay_degree:.3f} > {self.delay_threshold}，拒绝插入")
                    # 从remaining_orders中移除此订单，不再尝试它，因为它会导致超时
                    remaining_orders.remove(best_order)
                    if not remaining_orders: # 如果没有其他可尝试的订单了
                        break
                    else: # 否则，尝试下一个匹配度最高的订单
                        continue # 跳过下面的接受步骤，尝试下一个订单


                current_order_cluster.add(best_order)
                current_sequence = new_sequence
                remaining_orders.remove(best_order)
                
                new_og = OrderGroup(list(current_order_cluster), current_sequence.copy(), starting_order)
                all_order_groups.append(new_og)
        
        print(f"\n=== 时段{slot}订单组生成完成，共生成{len(all_order_groups)}个订单组 ===")
        
        # 去重，因为相同的订单组合可能从不同的starting_order生成
        unique_order_groups = []
        seen_order_sets = set()
        for og in sorted(all_order_groups, key=lambda x: (len(x.orders), tuple(sorted(x.orders)))):
            orders_tuple = tuple(sorted(og.orders))
            if orders_tuple not in seen_order_sets:
                unique_order_groups.append(og)
                seen_order_sets.add(orders_tuple)
        
        print(f"去重后剩余 {len(unique_order_groups)} 个订单组。")
        return unique_order_groups

class OGSA:
    """订单组选择算法 (Order Group Selection Algorithm)"""
    
    def __init__(self, food_delivery_optimizer):
        """
        初始化OGSA
        
        Args:
            food_delivery_optimizer: FoodDeliveryOptimizer实例
        """
        self.optimizer = food_delivery_optimizer
        
        # 从优化器获取参数
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        
        # 空间和订单信息
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        
        # OGSA特定参数（按照论文公式24）
        self.epsilon = 1.2  # 指数因子ε
        self.delta_max = 3  # 最大虚拟司机数量（按照附录K）
        
        print("OGSA初始化完成")
        print(f"OGSA参数 - epsilon: {self.epsilon}, delta_max: {self.delta_max}")
    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """计算两点间的欧几里得距离"""
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def calculate_sequence_total_distance(self, sequence: List[str], 
                                        start_pos: Tuple[float, float]) -> float:
        """计算序列的总配送距离"""
        if len(sequence) <= 1:
            return 0.0
        
        total_distance = 0.0
        current_pos = start_pos
        
        for node in sequence:
            # 解析节点信息
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id]
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            else:
                continue
            
            # 累加距离
            total_distance += self.calculate_distance(current_pos, next_pos)
            current_pos = next_pos
        
        return total_distance
    
    def estimate_sequence_delivery_times(self, sequence: List[str], start_time: float, 
                                       start_pos: Tuple[float, float]) -> Dict[int, float]:
        """估算序列中各订单的送达时间"""
        delivery_times = {}
        current_time = start_time
        current_pos = start_pos
        
        for node in sequence:
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id] 
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
                # 更新送达时间
                travel_time = self.calculate_distance(current_pos, next_pos) / self.driver_speed
                current_time += travel_time
                delivery_times[order_id] = current_time
                current_pos = next_pos
                continue
            else:
                continue
            
            # 计算旅行时间并更新位置
            travel_time = self.calculate_distance(current_pos, next_pos) / self.driver_speed
            current_time += travel_time
            current_pos = next_pos
        
        return delivery_times
    
    def calculate_order_group_dispatch_cost(self, order_group, slot: int, 
                                          virtual_driver_pos: Tuple[float, float], 
                                          virtual_driver_start_time: float) -> float:
        """
        计算订单组的调度成本（按照OGSA Step 4）
        
        Args:
            order_group: 订单组
            slot: 时段编号
            virtual_driver_pos: 虚拟司机位置
            virtual_driver_start_time: 虚拟司机开始时间
            
        Returns:
            总调度成本（旅行成本 + 延迟惩罚成本）
        """
        # 计算旅行成本
        total_distance = self.calculate_sequence_total_distance(
            order_group.sequence, virtual_driver_pos)
        travel_cost = total_distance * self.travel_cost_per_unit
        
        # 计算延迟惩罚成本
        slot_start_time = (slot - 1) * self.slot_duration
        delivery_times = self.estimate_sequence_delivery_times(
            order_group.sequence, virtual_driver_start_time, virtual_driver_pos)
        
        penalty_cost = 0.0
        for order_id, delivery_time in delivery_times.items():
            # 目标送达时间
            target_delivery_time = slot_start_time + self.estimated_delivery_duration[order_id]
            # 延迟时间
            delay_time = max(delivery_time - target_delivery_time, 0)
            # 延迟惩罚
            penalty_cost += delay_time * self.penalty_cost_per_unit
        
        total_cost = travel_cost + penalty_cost
        
        return total_cost
    
    def calculate_evaluation_function(self, dispatch_cost: float, num_orders: int) -> float:
        """
        计算订单组的评估函数值（按照论文公式24）
        
        Args:
            dispatch_cost: 调度成本c'_n
            num_orders: 订单数量|b_n|
            
        Returns:
            评估函数值 f(c'_n, |b_n|) = c'_n / |b_n|^ε
        """
        if num_orders <= 0:
            return float('inf')
        
        return dispatch_cost / (num_orders ** self.epsilon)
    
    def get_virtual_driver_info_for_og(self, order_group: OrderGroup, slot: int, 
                                     delta: int,
                                     actual_start_pos: Tuple[float, float] = None,
                                     actual_start_time: float = None) -> Tuple[Tuple[float, float], float]:
        """
        为订单组获取虚拟司机信息（按照OGSA Step 3）
        修正：允许传入实际的司机起始状态
        """
        
        # 如果提供了实际司机起始状态，则优先使用它们
        if actual_start_pos is not None and actual_start_time is not None:
            return actual_start_pos, actual_start_time

    
    def select_order_groups_for_slot(self, order_groups: List[OrderGroup], 
                                   all_orders: List[int], slot: int,
                                   actual_driver_start_pos: Tuple[float, float] = None,
                                   actual_driver_start_time: float = None) -> List[OrderGroup]:
        """
        为指定时段选择订单组（按照OGSA主算法）
        修正：将实际司机状态传递给成本计算
        """
        print(f"\n=== 开始OGSA为时段{slot}选择订单组 ===")
        print(f"输入订单组数量: {len(order_groups)}")
        print(f"需要覆盖的订单: {all_orders}")
        if actual_driver_start_pos and actual_driver_start_time is not None:
            print(f"OGSA使用实际司机起点: Pos={actual_driver_start_pos}, Time={actual_driver_start_time:.2f}")


        if not order_groups or not all_orders:
            return []
        
        remaining_order_groups = order_groups.copy()
        uncovered_orders = set(all_orders)
        selected_order_groups = []
        
        best_selection = None
        best_total_cost = float('inf')
        
        # 确定用于成本计算的起始位置和时间
        cost_calc_start_pos = actual_driver_start_pos if actual_driver_start_pos is not None else self.driver_start_coords[0]
        cost_calc_start_time = actual_driver_start_time if actual_driver_start_time is not None else (slot - 1) * self.slot_duration

        for delta_val in range(1, min(self.delta_max + 1, len(self.driver_start_coords) + 1 if len(order_groups) > 0 else 2)): #确保delta至少为1
            
            current_remaining = remaining_order_groups.copy()
            current_uncovered = set(all_orders)
            current_selected = []
            iteration = 0
            
            while current_uncovered and current_remaining:
                iteration += 1
                
                og_evaluations = []
                
                for og in current_remaining:

                    virtual_pos = cost_calc_start_pos
                    virtual_start_time = cost_calc_start_time

                    dispatch_cost = self.calculate_order_group_dispatch_cost(
                        og, slot, virtual_pos, virtual_start_time)
                    
                    eval_value = self.calculate_evaluation_function(dispatch_cost, len(og))
                    
                    og_orders = og.get_orders_set()
                    covered_new_orders = og_orders.intersection(current_uncovered)
                    
                    if covered_new_orders:
                        og_evaluations.append((og, dispatch_cost, eval_value, covered_new_orders))
                
                if not og_evaluations:
                    break
                
                og_evaluations.sort(key=lambda x: x[2])
                
                best_og, best_cost, best_eval, best_covered = og_evaluations[0]
                
                
                current_selected.append(best_og)
                current_uncovered -= best_covered
                
                selected_orders_set = best_og.get_orders_set()
                remaining_after_removal = []
                for og_item in current_remaining:
                    if not og_item.get_orders_set().intersection(selected_orders_set):
                        remaining_after_removal.append(og_item)
                current_remaining = remaining_after_removal
                
            
            if not current_uncovered:
                current_total_cost = sum(self.calculate_order_group_dispatch_cost(
                    sel_og, slot, cost_calc_start_pos, cost_calc_start_time)
                    for sel_og in current_selected)
                
                
                if current_total_cost < best_total_cost:
                    best_total_cost = current_total_cost
                    best_selection = current_selected.copy()
        
        if best_selection is None:
            print("警告：OGSA无法找到完全覆盖的解决方案，尝试备选策略（逐个添加单订单组）")
            best_selection = []
            temp_uncovered = set(all_orders)
            sorted_order_groups = sorted(order_groups, key=lambda og: (len(og.orders), tuple(sorted(og.orders))))
            for order_id_to_cover in sorted(list(all_orders)):
                if order_id_to_cover in temp_uncovered:
                    best_og_for_this_order = None
                    for og_candidate in sorted_order_groups:
                        if order_id_to_cover in og_candidate.orders:
                            is_compatible = True
                            for sel_og in best_selection:
                                if sel_og.get_orders_set().intersection(og_candidate.get_orders_set()):
                                    is_compatible = False
                                    break
                            if is_compatible:
                                best_og_for_this_order = og_candidate
                                break
                    if best_og_for_this_order:
                        best_selection.append(best_og_for_this_order)
                        temp_uncovered -= best_og_for_this_order.get_orders_set()
            if temp_uncovered:
                 print(f"警告: OGSA备选策略后仍有未覆盖订单: {sorted(temp_uncovered)}")


        
        print(f"\n=== OGSA完成，选择了{len(best_selection)}个订单组 ===")
        final_print_start_pos = actual_driver_start_pos if actual_driver_start_pos is not None else self.driver_start_coords[0]
        final_print_start_time = actual_driver_start_time if actual_driver_start_time is not None else (slot - 1) * self.slot_duration

        for i, og in enumerate(best_selection):
            cost = self.calculate_order_group_dispatch_cost(og, slot, final_print_start_pos, final_print_start_time)
            print(f"选择的OG{i+1}: 订单{og.orders}, 基于该起点的成本{cost:.3f}")
        
        return best_selection

class Driver:
    """司机状态类"""
    def __init__(self, driver_id: int, start_pos: Tuple[float, float], start_time: float, capacity: int):
        self.driver_id = driver_id
        self.current_pos = start_pos
        self.current_time = start_time
        self.remaining_capacity = capacity
        self.assigned_orders = []
        self.total_cost = 0.0
        self.total_distance = 0.0
        
    def can_handle_order_group(self, order_group) -> bool:
        """检查司机是否能处理该订单组"""
        return self.remaining_capacity >= len(order_group)
    
    def assign_order_group(self, order_group, execution_cost: float, new_pos: Tuple[float, float], 
                          new_time: float, distance: float):
        """分配订单组给司机"""
        self.assigned_orders.extend(order_group.orders)
        self.remaining_capacity -= len(order_group)
        self.total_cost += execution_cost
        self.total_distance += distance
        self.current_pos = new_pos
        self.current_time = new_time

class OGAH:
    """订单组分配启发式算法 (Order Group Assignment Heuristic) - 严格按照附录L实现"""
    
    def __init__(self, food_delivery_optimizer):
        """
        初始化OGAH
        
        Args:
            food_delivery_optimizer: FoodDeliveryOptimizer实例
        """
        self.optimizer = food_delivery_optimizer
        
        # 从优化器获取参数（与Gurobi.py完全一致）
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        
        # 空间和订单信息
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.num_drivers = food_delivery_optimizer.num_drivers
        
        self.current_driver_states = None # 用于在时段间传递司机状态

        print("OGAH初始化完成 - 严格按照附录L实现")
        print(f"OGAH参数 - 司机数量: {self.num_drivers}, 最大订单数: {self.max_orders_per_driver}")
    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        """计算两点间的欧几里得距离"""
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def calculate_sequence_execution_details(self, sequence: List[str], driver_start_pos: Tuple[float, float], 
                                           driver_start_time: float, slot: int) -> Tuple[float, float, Tuple[float, float], float]:
        """
        计算司机执行序列的详细信息
        
        Returns:
            (travel_cost, penalty_cost, final_pos, final_time)
        """
        if not sequence:
            return 0.0, 0.0, driver_start_pos, driver_start_time
        
        current_pos = driver_start_pos
        current_time = driver_start_time
        total_distance = 0.0
        total_penalty = 0.0
        slot_start_time = (slot - 1) * self.slot_duration
        
        # 执行序列中的每个节点
        for i, node in enumerate(sequence):
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id]
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            else:
                continue
            
            # 计算旅行距离和时间
            travel_distance = self.calculate_distance(current_pos, next_pos)
            travel_time = travel_distance / self.driver_speed
            
            # 更新位置和时间
            total_distance += travel_distance
            current_time += travel_time
            current_pos = next_pos
            
            # 如果是送餐节点，计算延迟惩罚
            if node.startswith("delivery_"):
                target_delivery_time = slot_start_time + self.estimated_delivery_duration[order_id]
                delay_time = max(current_time - target_delivery_time, 0)
                total_penalty += delay_time * self.penalty_cost_per_unit
        
        travel_cost = total_distance * self.travel_cost_per_unit
        
        return travel_cost, total_penalty, current_pos, current_time
    
    def calculate_driver_dispatch_cost(self, order_group, driver: Driver, slot: int) -> Tuple[float, Dict]:
        """
        计算司机执行订单组的调度成本f_nk（按照附录L）
        
        Args:
            order_group: 订单组
            driver: 司机对象
            slot: 时段编号
            
        Returns:
            (total_cost, execution_details)
        """
        # 检查司机容量约束
        if not driver.can_handle_order_group(order_group):
            return float('inf'), {}
        
        # 计算执行成本
        travel_cost, penalty_cost, final_pos, final_time = self.calculate_sequence_execution_details(
            order_group.sequence, driver.current_pos, driver.current_time, slot)
        
        total_cost = travel_cost + penalty_cost
        
        execution_details = {
            'travel_cost': travel_cost,
            'penalty_cost': penalty_cost,
            'total_distance': travel_cost / self.travel_cost_per_unit if self.travel_cost_per_unit > 0 else 0,
            'final_pos': final_pos,
            'final_time': final_time
        }
        
        return total_cost, execution_details
    
    def calculate_regret_value(self, dispatch_costs: List[float]) -> float:
        """
        计算订单组的regret值（按照公式25）
        ĉ*_n = Σ(j=1 to 3)(f_{n,x_{nj}} - f_{n,x_{n1}})
        
        Args:
            dispatch_costs: 按成本升序排列的调度成本列表
            
        Returns:
            regret值
        """
        if len(dispatch_costs) < 2:
            return 0.0
        
        # 确保按升序排列
        costs = sorted(dispatch_costs)
        best_cost = costs[0]
        
        # 计算regret-3值
        regret_value = 0.0
        for i in range(1, min(4, len(costs))):  # 最多考虑前4个成本
            regret_value += costs[i] - best_cost
        
        return regret_value
    
    def initialize_drivers(self, slot: int, initial_states: List[Driver] = None) -> List[Driver]:
        """初始化司机状态"""
        drivers = []
        slot_start_time = (slot - 1) * self.slot_duration
        
        if initial_states:
            print(f"  OGAH: 使用来自前一时段的司机状态初始化时段 {slot}")
            for i, prev_driver_state in enumerate(initial_states):
                # 司机的开始时间是其前一时段结束时间与当前时段名义开始时间中的较晚者
                start_time_for_current_slot = max(prev_driver_state.current_time, slot_start_time)
                driver = Driver(
                    driver_id=prev_driver_state.driver_id,
                    start_pos=prev_driver_state.current_pos, 
                    start_time=start_time_for_current_slot,
                    capacity=self.max_orders_per_driver # 容量在新时段重置
                )
                # 重置上一时段的成本和已分配订单，因为这是新时段的开始
                driver.total_cost = 0.0
                driver.total_distance = 0.0
                driver.assigned_orders = []
                driver.remaining_capacity = self.max_orders_per_driver # 确保容量已重置
                drivers.append(driver)
                print(f"    司机 {driver.driver_id}: 从位置 {driver.current_pos} @ 时间 {driver.current_time:.2f} 开始")
        else:
            print(f"  OGAH: 为时段 {slot} 初始化新的司机状态")
            for driver_id in range(self.num_drivers):
                start_pos = self.driver_start_coords[driver_id] if driver_id in self.driver_start_coords else self.driver_start_coords[0]
                driver = Driver(driver_id, start_pos, slot_start_time, self.max_orders_per_driver)
                drivers.append(driver)
                print(f"    司机 {driver.driver_id}: 从位置 {driver.current_pos} @ 时间 {driver.current_time:.2f} 开始")
        
        return drivers
    
    def assign_order_groups_to_drivers(self, selected_order_groups: List, slot: int, initial_driver_states: List[Driver] = None) -> Tuple[float, List[Driver], Dict]:
        """
        将选择的订单组分配给司机（按照附录L Steps 1-7）
        
        Args:
            selected_order_groups: OGSA选择的订单组列表
            slot: 时段编号
            
        Returns:
            (total_cost, drivers, assignment_details)
        """
        print(f"\n=== 开始OGAH为时段{slot}分配订单组 ===")
        print(f"待分配订单组数量: {len(selected_order_groups)}")
        
        if not selected_order_groups:
            return 0.0, [], {}
        
        # Step 1: 初始化司机状态
        drivers = self.initialize_drivers(slot, initial_driver_states)
        self.current_driver_states = drivers # 保存当前司机状态，以便传递

        available_drivers = drivers.copy()
        unassigned_order_groups = selected_order_groups.copy()
        assignment_details = {
            'assignments': [],
            'total_travel_cost': 0.0,
            'total_penalty_cost': 0.0,
            'unassigned_groups': []
        }
        
        iteration = 0
        
        # Steps 4-7: 迭代分配过程
        while unassigned_order_groups and available_drivers:
            iteration += 1
            print(f"\n--- OGAH 迭代 {iteration} ---")
            print(f"剩余未分配订单组: {len(unassigned_order_groups)}")
            print(f"可用司机数: {len(available_drivers)}")
            
            # Step 3: 计算所有订单组对所有司机的调度成本
            og_evaluations = []
            
            for og_idx, order_group in enumerate(unassigned_order_groups):
                driver_costs = []
                driver_details = []
                
                # 计算该订单组分配给每个可用司机的成本
                for driver in available_drivers:
                    cost, details = self.calculate_driver_dispatch_cost(order_group, driver, slot)
                    driver_costs.append(cost)
                    driver_details.append((driver, details))
                
                # 过滤掉无法处理的司机（成本为无穷大）
                valid_assignments = [(cost, driver, details) for cost, (driver, details) in 
                                   zip(driver_costs, driver_details) if cost < float('inf')]
                
                if valid_assignments:
                    # 按成本排序
                    valid_assignments.sort(key=lambda x: x[0])
                    
                    # Step 6: 计算regret值
                    costs_only = [cost for cost, _, _ in valid_assignments]
                    regret_value = self.calculate_regret_value(costs_only)
                    
                    # 记录最佳分配方案
                    best_cost, best_driver, best_details = valid_assignments[0]
                    
                    og_evaluations.append({
                        'order_group': order_group,
                        'og_index': og_idx,
                        'regret_value': regret_value,
                        'best_cost': best_cost,
                        'best_driver': best_driver,
                        'best_details': best_details,
                        'valid_assignments': valid_assignments
                    })
            
            if not og_evaluations:
                print("    没有可分配的订单组，停止分配")
                break
            
            # Step 7: 选择regret值最大的订单组
            og_evaluations.sort(key=lambda x: x['regret_value'], reverse=True)
            best_assignment = og_evaluations[0]
            
            selected_og = best_assignment['order_group']
            selected_driver = best_assignment['best_driver']
            execution_details = best_assignment['best_details']
            assignment_cost = best_assignment['best_cost']
            
            print(f"    选择订单组: 订单{selected_og.orders}")
            print(f"    分配给司机{selected_driver.driver_id}")
            print(f"    Regret值: {best_assignment['regret_value']:.3f}")
            print(f"    执行成本: {assignment_cost:.3f}")
            print(f"      旅行成本: {execution_details['travel_cost']:.3f}")
            print(f"      延迟惩罚: {execution_details['penalty_cost']:.3f}")
            
            # 执行分配
            selected_driver.assign_order_group(
                selected_og, 
                assignment_cost,
                execution_details['final_pos'],
                execution_details['final_time'],
                execution_details['total_distance']
            )
            
            # 记录分配详情
            assignment_details['assignments'].append({
                'order_group': selected_og,
                'driver_id': selected_driver.driver_id,
                'cost': assignment_cost,
                'travel_cost': execution_details['travel_cost'],
                'penalty_cost': execution_details['penalty_cost']
            })
            
            assignment_details['total_travel_cost'] += execution_details['travel_cost']
            assignment_details['total_penalty_cost'] += execution_details['penalty_cost']
            
            # 更新状态
            unassigned_order_groups.remove(selected_og)
            
            # 如果司机容量用完，从可用司机列表中移除
            if selected_driver.remaining_capacity <= 0:
                available_drivers.remove(selected_driver)
        
        # Step 5: 处理剩余未分配的订单组（如果有）
        if unassigned_order_groups:
            print(f"\n警告: 有{len(unassigned_order_groups)}个订单组未能分配")
            # 按照附录L的处理方式：将剩余订单组连接到成本最低的司机
            if drivers:
                # 找到总成本最低的司机
                best_driver = min(drivers, key=lambda d: d.total_cost)
                for og in unassigned_order_groups:
                    print(f"    将未分配订单组{og.orders}连接到司机{best_driver.driver_id}")
                    # 简化处理：直接添加订单，不重新计算路径
                    best_driver.assigned_orders.extend(og.orders)
                    assignment_details['unassigned_groups'].append(og)
        
        # 计算总成本
        total_cost = assignment_details['total_travel_cost'] + assignment_details['total_penalty_cost']
        
        print(f"\n=== OGAH完成，总成本: {total_cost:.3f} ===")
        print(f"总旅行成本: {assignment_details['total_travel_cost']:.3f}")
        print(f"总延迟惩罚: {assignment_details['total_penalty_cost']:.3f}")
        
        return total_cost, drivers, assignment_details

class SOCDA:
    """单波次订单整合调度算法 (Single Order Consolidation Dispatching Algorithm)"""
    
    def __init__(self, food_delivery_optimizer):
        """
        初始化SOCDA，整合OGGM + OGSA + OGAH
        
        Args:
            food_delivery_optimizer: FoodDeliveryOptimizer实例
        """
        self.optimizer = food_delivery_optimizer
        
        # 初始化三个子算法
        self.oggm = OGGM(food_delivery_optimizer)
        self.ogsa = OGSA(food_delivery_optimizer)
        self.ogah = OGAH(food_delivery_optimizer)
        
        print("SOCDA初始化完成 - 整合OGGM + OGSA + OGAH")
    
    def solve_single_slot_dispatch(self, orders_in_slot: List[int], slot: int, 
                                 initial_driver_states_for_ogah: List[Driver] = None) -> Tuple[float, float, List[Driver], Dict]:
        """
        求解单个时段的调度问题（完整的SOCDA流程）
        修正: 将实际司机状态传递给OGGM和OGSA
        """
        print(f"\n{'='*60}")
        print(f"SOCDA求解时段{slot}调度问题")
        print(f"订单: {orders_in_slot}")
        
        actual_start_pos_for_slot = None
        actual_start_time_for_slot = None
        
        # 如果有上一时段的司机状态，用第一个司机的结束状态作为本时段OGGM/OGSA的起点参考
        # OGAH的initialize_drivers会更精细地处理每个司机的起始状态
        if initial_driver_states_for_ogah and len(initial_driver_states_for_ogah) > 0:
            # 以第一个司机为例，作为OGGM/OGSA的"代表性"起始点
            # OGAH的initialize_drivers会正确处理每个司机的起始时间
            representative_driver_prev_state = initial_driver_states_for_ogah[0]
            actual_start_pos_for_slot = representative_driver_prev_state.current_pos
            nominal_slot_start_time = (slot - 1) * self.optimizer.slot_duration
            actual_start_time_for_slot = max(representative_driver_prev_state.current_time, nominal_slot_start_time)
            print(f"SOCDA: 时段{slot}将使用代表性司机起点: Pos={actual_start_pos_for_slot}, Time={actual_start_time_for_slot:.2f} for OGGM/OGSA")

        if not orders_in_slot:
            current_slot_drivers = []
            if initial_driver_states_for_ogah:
                slot_nominal_start_time = (slot - 1) * self.optimizer.slot_duration
                for prev_driver in initial_driver_states_for_ogah:
                    current_driver = copy.deepcopy(prev_driver)
                    current_driver.current_time = max(prev_driver.current_time, slot_nominal_start_time)
                    current_driver.remaining_capacity = self.optimizer.max_orders_per_driver
                    current_driver.assigned_orders = []
                    current_driver.total_cost = 0.0
                    current_driver.total_distance = 0.0
                    current_slot_drivers.append(current_driver)
            else: # 如果也没有初始状态 (比如第一个时段且无订单)
                # OGAH会创建新的司机，所以这里可以返回空列表，OGAH会处理
                 pass


            return 0.0, 0.0, current_slot_drivers, {'assignments': [], 'message': 'No orders to dispatch'}
        
        try:
            print(f"\n--- Step 1: OGGM生成订单组 ---")
            order_groups = self.oggm.generate_order_groups_for_slot(
                orders_in_slot, slot, actual_start_pos_for_slot, actual_start_time_for_slot
            )
            
            if not order_groups:
                print("警告：OGGM未生成任何订单组")
                # 返回上一时段的状态，如果适用
                return 0.0, 0.0, initial_driver_states_for_ogah if initial_driver_states_for_ogah else [], {'assignments': [], 'message': 'No order groups generated'}
            
            print(f"\n--- Step 2: OGSA选择订单组 ---")
            selected_order_groups = self.ogsa.select_order_groups_for_slot(
                order_groups, orders_in_slot, slot, actual_start_pos_for_slot, actual_start_time_for_slot
            )
            
            if not selected_order_groups:
                print("警告：OGSA未选择任何订单组")
                return 0.0, 0.0, initial_driver_states_for_ogah if initial_driver_states_for_ogah else [], {'assignments': [], 'message': 'No order groups selected'}
            
            print(f"\n--- Step 3: OGAH分配订单组给司机 --- ")
            total_cost, drivers_final_state, assignment_details = self.ogah.assign_order_groups_to_drivers(
                selected_order_groups, slot, initial_driver_states_for_ogah
            )
            
            # 提取旅行成本和延迟惩罚成本
            travel_cost = assignment_details['total_travel_cost']
            penalty_cost = assignment_details['total_penalty_cost']
            
            print(f"\n=== SOCDA时段{slot}求解完成 ===")
            print(f"旅行成本: {travel_cost:.3f}")
            print(f"延迟惩罚: {penalty_cost:.3f}")
            print(f"总成本: {total_cost:.3f}")
            
            # 构建详细结果
            dispatch_details = {
                'slot': slot,
                'orders': orders_in_slot,
                'num_order_groups_generated': len(order_groups),
                'num_order_groups_selected': len(selected_order_groups),
                'assignments': assignment_details['assignments'],
                'drivers': [{'driver_id': d.driver_id, 'assigned_orders': d.assigned_orders, 
                           'current_pos': d.current_pos, 'current_time': d.current_time, # 添加司机最终状态
                           'total_cost': d.total_cost} for d in drivers_final_state],
                'total_travel_cost': travel_cost,
                'total_penalty_cost': penalty_cost,
                'total_cost': total_cost
            }
            
            return travel_cost, penalty_cost, drivers_final_state, dispatch_details
            
        except Exception as e:
            print(f"SOCDA求解时段{slot}出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0.0, 0.0, [], {'error': str(e)} # 返回初始状态以防错误

def test_complete_socda():
    """完整测试SOCDA算法，验证与Gurobi.py结果的一致性"""
    print("="*80)
    print("SOCDA完整算法测试 - 复现论文3.3案例")
    print("="*80)
    
    # 模拟FoodDeliveryOptimizer（与Gurobi.py完全一致）
    class MockFoodDeliveryOptimizer:
        def __init__(self):
            # 基本参数设置 - 严格按照Gurobi.py
            self.commission_rate = 0.18
            self.travel_cost_per_unit = 0.2  
            self.penalty_cost_per_unit = 1.0
            self.slot_duration = 25
            self.driver_speed = 1.0
            self.max_orders_per_driver = 10
            self.num_drivers = 1  # 论文案例中只有1个司机
            
            # 订单基本信息 - 严格按照论文Table 1
            self.order_values = [31, 22, 36, 43]
            self.order_restaurants = [0, 1, 0, 1]  # O1,O3→R1, O2,O4→R2
            self.estimated_delivery_duration = [12, 15, 18, 20]
            
            # 顾客偏好列表 - 严格按照论文Table 1
            self.customer_preferences = {
                0: {(1,3): 1, (1,6): 2, (2,3): 4, (2,6): 5, 'no_purchase': 3},
                1: {(1,3): 1, (1,6): 2, (2,3): 3, (2,6): 4, 'no_purchase': 5},
                2: {(1,3): 1, (1,6): 4, (2,3): 2, (2,6): 5, 'no_purchase': 3},
                3: {(1,3): 4, (1,6): 5, (2,3): 1, (2,6): 2, 'no_purchase': 3}
            }
            
            # 空间布局设置 - 严格按照Gurobi.py
            self.restaurant_coords = {0: (2, 2), 1: (8, 2)}
            self.customer_coords = {0: (1, 6), 1: (9, 6), 2: (3, 8), 3: (7, 8)}
            self.driver_start_coords = {0: (5, 1)}
        
        def get_customer_choice(self, customer_id, available_tpcs):
            """根据偏好获取顾客选择（与Gurobi.py一致）"""
            preferences = self.customer_preferences[customer_id]
            available_options = list(available_tpcs) + ['no_purchase']
            
            best_choice = min(available_options, 
                             key=lambda x: preferences.get(x, float('inf')))
            
            return None if best_choice == 'no_purchase' else best_choice
        
        def get_all_customer_choices(self, price_slot1, price_slot2):
            """获取所有顾客在给定价格下的选择（与Gurobi.py一致）"""
            available_tpcs = [(1, price_slot1), (2, price_slot2)]
            choices = []
            
            for customer_id in range(4):  # 4个顾客
                choice = self.get_customer_choice(customer_id, available_tpcs)
                choices.append(choice)
            
            return choices
    
    try:
        # 创建模拟优化器
        mock_optimizer = MockFoodDeliveryOptimizer()
        
        # 创建SOCDA实例
        socda = SOCDA(mock_optimizer)
        
        # 初始化用于在时段间传递的司机状态列表
        # 这个列表将包含每个司机在前一个时段结束时的状态对象
        current_drivers_state_list_socda: List[Driver] = None 

        # 测试所有四种定价方案
        pricing_schemes = [(3, 3), (3, 6), (6, 3), (6, 6)]
        all_results = []
        
        for price_slot1, price_slot2 in pricing_schemes:
            print(f"\n{'='*70}")
            print(f"测试定价方案: (时段1=¥{price_slot1}, 时段2=¥{price_slot2})")
            print(f"{'='*70}")
            
            # 获取顾客选择（与Gurobi.py逻辑一致）
            customer_choices = mock_optimizer.get_all_customer_choices(price_slot1, price_slot2)
            
            print("顾客选择:")
            slot1_orders = []
            slot2_orders = []
            
            for i, choice in enumerate(customer_choices):
                if choice:
                    choice_str = f"选择时段{choice[0]}, 支付¥{choice[1]}"
                    if choice[0] == 1:
                        slot1_orders.append(i)
                    else:
                        slot2_orders.append(i)
                else:
                    choice_str = "选择不购买"
                print(f"  顾客O{i+1}: {choice_str}")
            
            print(f"\n时段分配:")
            print(f"  时段1订单: {slot1_orders}")
            print(f"  时段2订单: {slot2_orders}")
            
            # 使用SOCDA求解每个时段
            total_travel_cost = 0.0
            total_penalty_cost = 0.0
            
            current_drivers_state_list_socda = None

            # 求解时段1
            if slot1_orders:
                travel1, penalty1, drivers_after_slot1, details1 = socda.solve_single_slot_dispatch(
                    slot1_orders, 1, current_drivers_state_list_socda
                )
                total_travel_cost += travel1
                total_penalty_cost += penalty1
                current_drivers_state_list_socda = drivers_after_slot1 # 更新司机状态
            else: 
                _, _, drivers_after_slot1, _ = socda.solve_single_slot_dispatch(
                    [], 1, current_drivers_state_list_socda 
                )
                current_drivers_state_list_socda = drivers_after_slot1


            # 求解时段2  
            if slot2_orders:
                travel2, penalty2, drivers_after_slot2, details2 = socda.solve_single_slot_dispatch(
                    slot2_orders, 2, current_drivers_state_list_socda
                )
                total_travel_cost += travel2
                total_penalty_cost += penalty2
            else: # 如果时段2也没有订单
                _, _, drivers_after_slot2, _ = socda.solve_single_slot_dispatch(
                    [], 2, current_drivers_state_list_socda
                )

            
            # 汇总结果
            total_cost = total_travel_cost + total_penalty_cost
            
            # 计算收入
            revenue = 0
            for i, choice in enumerate(customer_choices):
                if choice: # 如果顾客选择了购买
                    order_id = i # 顾客ID即订单ID
                    # 累加订单价值的佣金部分
                    revenue += mock_optimizer.order_values[order_id] * mock_optimizer.commission_rate
                    # 累加配送费
                    revenue += choice[1]  
            
            # 计算利润
            profit = revenue - total_cost
            
            result = {
                'scheme': (price_slot1, price_slot2),
                'revenue': revenue,
                'travel_cost': total_travel_cost,
                'penalty_cost': total_penalty_cost,
                'total_cost': total_cost,
                'profit': profit
            }
            
            all_results.append(result)
            
            print(f"\n--- 方案{(price_slot1, price_slot2)}汇总 ---")
            print(f"总收入: {revenue:.2f}")
            print(f"总旅行成本: {total_travel_cost:.2f}")
            print(f"总延迟惩罚: {total_penalty_cost:.2f}")
            print(f"总运营成本: {total_cost:.2f}")
            print(f"总利润: {profit:.2f}")
        
        # 找出最优方案（利润最大）
        best_scheme = max(all_results, key=lambda x: x['profit'])
        
        # 结果对比
        print(f"\n{'='*80}")
        print("所有定价方案结果汇总对比")
        print(f"{'='*80}")
        print(f"{'方案':<10} {'收入':<15} {'旅行成本':<15} {'延迟成本':<15} {'总成本':<15} {'利润':<15} {'最优':<10}")
        print("-" * 80)
        
        for result in all_results:
            scheme = result['scheme']
            revenue = result['revenue']
            travel_cost = result['travel_cost']
            penalty_cost = result['penalty_cost'] 
            total_cost = result['total_cost']
            profit = result['profit']
            
            is_best = "★" if result == best_scheme else ""
            
            scheme_str = f"({scheme[0]}, {scheme[1]})"
            print(f"{scheme_str:<10} {revenue:<15.2f} {travel_cost:<15.2f} {penalty_cost:<15.2f} {total_cost:<15.2f} {profit:<15.2f} {is_best:<10}")
        
        return socda, all_results
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    # 运行完整测试
    socda_instance, test_results = test_complete_socda()