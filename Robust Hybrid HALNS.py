import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import random
import tempfile
import os
from datetime import datetime, timedelta
import copy
from itertools import product
import math
import time
from typing import List, Dict, Tuple, Any, Set
import gurobipy as gp
from gurobipy import GRB
from types import SimpleNamespace
from math import radians, sin, cos, asin, sqrt


# =================================================================================
# == 代码从 Robust Hybrid.py 移植并适配 (开始)
# =================================================================================

def haversine(lon1, lat1, lon2, lat2):
    """计算两点间的地理距离（单位：公里）"""
    if any(pd.isna(x) for x in [lon1, lat1, lon2, lat2]):
        return 0.0
    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
    dlon = lon2 - lon1 
    dlat = lat2 - lat1 
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a)) 
    r = 6371  # 地球平均半径，单位为公里
    return c * r

def compute_rg2_robust_cost(sequence, start_pos, start_time, stp_start_time,
                             restaurant_coords, customer_coords,
                             order_restaurants,
                             driver_speed,
                             theta_t, deviation_ratio,
                             travel_cost_per_unit, penalty_cost_per_unit,
                             est_delivery_duration, depot_coord):
    """返回 (robust_travel_cost, robust_penalty_cost, total_cost, final_pos, final_time)"""
    if not sequence:
        return 0.0, 0.0, 0.0, start_pos, start_time

    arcs = []
    deliveries = []  # (cust_id, arc_idx)
    cur = start_pos
    for node in sequence:
        if node.startswith("pickup_"):
            idx = int(node.split("_")[1])
            rest_id = order_restaurants[idx]
            nxt = restaurant_coords[rest_id]
        elif node.startswith("delivery_"):
            idx = int(node.split("_")[1])
            nxt = customer_coords[idx]
            deliveries.append((idx, len(arcs)))
        else:
            continue
        arcs.append((cur, nxt))
        cur = nxt

    nominal_times, deviations = [], []
    for a, b in arcs:
        dist = haversine(a[0], a[1], b[0], b[1])
        t_nom = dist / driver_speed
        nominal_times.append(t_nom)
        deviations.append(t_nom * deviation_ratio)

    positive_arc_count = sum(1 for t in nominal_times if t > 1e-8)
    gamma = math.ceil(theta_t * positive_arc_count)

    prefix_nom = 0.0
    dev_prefix = []
    arrival_worst = {}
    for arc_idx, (t_nom, dev) in enumerate(zip(nominal_times, deviations)):
        prefix_nom += t_nom
        dev_prefix.append(dev)
        worst_extra_prefix = sum(sorted(dev_prefix, reverse=True)[:gamma])
        time_now = start_time + prefix_nom + worst_extra_prefix
        for cust_id, idx_in_arc in deliveries:
            if idx_in_arc == arc_idx:
                arrival_worst[cust_id] = time_now

    worst_extra_total = sum(sorted(deviations, reverse=True)[:gamma])
    worst_total_time = start_time + sum(nominal_times) + worst_extra_total

    final_service_pos = arcs[-1][1] if arcs else start_pos
    final_service_time = worst_total_time

    robust_travel_cost = (worst_total_time - start_time) * travel_cost_per_unit

    robust_penalty_cost = 0.0
    for cust_id, worst_arrival in arrival_worst.items():
        if cust_id in est_delivery_duration:
            target = stp_start_time + est_delivery_duration[cust_id]
            delay = max(worst_arrival - target, 0)
            robust_penalty_cost += delay * penalty_cost_per_unit

    total_cost = robust_travel_cost + robust_penalty_cost
    return robust_travel_cost, robust_penalty_cost, total_cost, final_service_pos, final_service_time


class OrderGroup:
    """订单组类，包含订单序列和相关信息"""
    def __init__(self, orders: List[int], sequence: List[str], starting_order: int):
        self.orders = orders
        self.sequence = sequence
        self.starting_order = starting_order
        self.virtual_driver_id = None
        self.estimated_cost = 0.0
        self.dispatch_cost = 0.0
        
    def __len__(self):
        return len(self.orders)
        
    def contains_order(self, order_id: int) -> bool:
        return order_id in self.orders
    
    def get_orders_set(self) -> Set[int]:
        return set(self.orders)


class HybridOGGM:
    """订单组生成方法 (Order Group Generation Method) - 适配版"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.delay_threshold = 0.2
    
    def calculate_distance(self, coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
        return haversine(coord1[0], coord1[1], coord2[0], coord2[1])
    
    def get_order_coordinates(self, order_id: int) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        restaurant_id = self.order_restaurants[order_id]
        restaurant_coord = self.restaurant_coords[restaurant_id]
        customer_coord = self.customer_coords[order_id]
        return restaurant_coord, customer_coord

    def calculate_single_order_distance(self, order_id: int, driver_start: Tuple[float, float]) -> float:
        restaurant_coord, customer_coord = self.get_order_coordinates(order_id)
        dist_to_restaurant = self.calculate_distance(driver_start, restaurant_coord)
        dist_restaurant_to_customer = self.calculate_distance(restaurant_coord, customer_coord)
        return dist_to_restaurant + dist_restaurant_to_customer

    def calculate_two_orders_combined_distance(self, order1_id: int, order2_id: int, driver_start: Tuple[float, float]) -> Tuple[float, float]:
        rest1_coord, cust1_coord = self.get_order_coordinates(order1_id)
        rest2_coord, cust2_coord = self.get_order_coordinates(order2_id)
        
        dist_start_to_r1 = self.calculate_distance(driver_start, rest1_coord)
        sequences_order1_first = [
            dist_start_to_r1 + self.calculate_distance(rest1_coord, rest2_coord) + self.calculate_distance(rest2_coord, cust2_coord) + self.calculate_distance(cust2_coord, cust1_coord),
            dist_start_to_r1 + self.calculate_distance(rest1_coord, rest2_coord) + self.calculate_distance(rest2_coord, cust1_coord) + self.calculate_distance(cust1_coord, cust2_coord),
            dist_start_to_r1 + self.calculate_distance(rest1_coord, cust1_coord) + self.calculate_distance(cust1_coord, rest2_coord) + self.calculate_distance(rest2_coord, cust2_coord)
        ]
        dist_order1_first = min(sequences_order1_first)
        
        dist_start_to_r2 = self.calculate_distance(driver_start, rest2_coord)
        sequences_order2_first = [
            dist_start_to_r2 + self.calculate_distance(rest2_coord, rest1_coord) + self.calculate_distance(rest1_coord, cust1_coord) + self.calculate_distance(cust1_coord, cust2_coord),
            dist_start_to_r2 + self.calculate_distance(rest2_coord, rest1_coord) + self.calculate_distance(rest1_coord, cust2_coord) + self.calculate_distance(cust2_coord, cust1_coord),
            dist_start_to_r2 + self.calculate_distance(rest2_coord, cust2_coord) + self.calculate_distance(cust2_coord, rest1_coord) + self.calculate_distance(rest1_coord, cust1_coord)
        ]
        dist_order2_first = min(sequences_order2_first)
        return dist_order1_first, dist_order2_first
    
    def calculate_order_matching_degree(self, order1_id: int, order2_id: int, virtual_driver1_pos: Tuple[float, float], virtual_driver2_pos: Tuple[float, float]) -> Tuple[float, float]:
        dist1_separate = self.calculate_single_order_distance(order1_id, virtual_driver1_pos)
        dist2_separate = self.calculate_single_order_distance(order2_id, virtual_driver2_pos)
        total_separate_distance = dist1_separate + dist2_separate
        dist_order1_first, dist_order2_first = self.calculate_two_orders_combined_distance(order1_id, order2_id, virtual_driver1_pos)
        fit_o1_o2 = total_separate_distance - dist_order1_first
        fit_o2_o1 = total_separate_distance - dist_order2_first
        return fit_o1_o2, fit_o2_o1

    def cheapest_insertion_single_order(self, sequence: List[str], new_order_id: int, current_start_pos: Tuple[float, float]) -> Tuple[List[str], float]:
        """
        根据论文J.4的最便宜插入法，计算新序列和距离 (d_star)
        规则: 先用最便宜插入法插入餐厅点，然后将顾客点插入到紧随其后的位置
        """
        if not sequence:
            new_sequence = [f"pickup_{new_order_id}", f"delivery_{new_order_id}"]
            new_dist = self.calculate_sequence_total_distance(new_sequence, current_start_pos)
            return new_sequence, new_dist

        best_sequence = None
        min_dist = float('inf')
        
        pickup_node = f"pickup_{new_order_id}"
        delivery_node = f"delivery_{new_order_id}"
        
        # 遍历所有可能的取餐点插入位置
        for pickup_pos in range(len(sequence) + 1):
            # 插入取餐点，然后紧接着插入送餐点
            temp_sequence = sequence[:pickup_pos] + [pickup_node, delivery_node] + sequence[pickup_pos:]
            
            dist = self.calculate_sequence_total_distance(temp_sequence, current_start_pos)
            if dist < min_dist:
                min_dist = dist
                best_sequence = temp_sequence
        
        return best_sequence, min_dist

    def calculate_sequence_total_distance(self, sequence: List[str], start_pos: Tuple[float, float]) -> float:
        if not sequence:
            return 0.0
        
        total_distance = 0.0
        current_pos = start_pos
        
        for node in sequence:
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                restaurant_id = self.order_restaurants[order_id]
                next_pos = self.restaurant_coords[restaurant_id]
            elif node.startswith("delivery_"):
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            else:
                continue
            total_distance += self.calculate_distance(current_pos, next_pos)
            current_pos = next_pos
        return total_distance
    
    def calculate_robust_arrival_times(self, sequence: List[str], start_time: float, start_pos: Tuple[float, float]) -> Dict[int, float]:
        """Calculates the worst-case arrival time for each order in a sequence."""
        if not sequence:
            return {}

        arcs = []
        deliveries = {}  # {arc_idx: [order_id, ...]}
        cur = start_pos
        for node in sequence:
            if node.startswith("pickup_"):
                idx = int(node.split("_")[1])
                rest_id = self.optimizer.order_restaurants[idx]
                nxt = self.optimizer.restaurant_coords[rest_id]
            elif node.startswith("delivery_"):
                idx = int(node.split("_")[1])
                nxt = self.optimizer.customer_coords[idx]
                if len(arcs) not in deliveries:
                    deliveries[len(arcs)] = []
                deliveries[len(arcs)].append(idx)
            else:
                continue
            arcs.append((cur, nxt))
            cur = nxt

        nominal_times, deviations = [], []
        for a, b in arcs:
            dist = self.calculate_distance(a, b)
            t_nom = dist / self.optimizer.driver_speed
            nominal_times.append(t_nom)
            deviations.append(t_nom * self.optimizer.travel_time_deviation_ratio)

        positive_arc_count = sum(1 for t in nominal_times if t > 1e-8)
        gamma = math.ceil(self.optimizer.theta_t * positive_arc_count)

        prefix_nom = 0.0
        dev_prefix = []
        arrival_worst = {}
        for arc_idx, (t_nom, dev) in enumerate(zip(nominal_times, deviations)):
            prefix_nom += t_nom
            dev_prefix.append(dev)
            worst_extra_prefix = sum(sorted(dev_prefix, reverse=True)[:gamma])
            time_now = start_time + prefix_nom + worst_extra_prefix
            
            if arc_idx in deliveries:
                for cust_id in deliveries[arc_idx]:
                    arrival_worst[cust_id] = time_now
                    
        return arrival_worst

    def calculate_order_delay_degree(self, order_id: int, actual_delivery_time: float, stp_start_time: float) -> float:
        if order_id not in self.estimated_delivery_duration:
             return 0.0
        target_delivery_time = stp_start_time + self.estimated_delivery_duration[order_id]
        delay_time = max(actual_delivery_time - target_delivery_time, 0)
        return delay_time / self.estimated_delivery_duration[order_id] if self.estimated_delivery_duration[order_id] > 0 else 0.0

    def calculate_max_delay_degree_in_sequence(self, sequence: List[str], start_time: float, stp_start_time: float, start_pos: Tuple[float, float]) -> float:
        """Calculates the max delay degree in a sequence using ROBUST arrival times."""
        # This now calls the new robust calculation method
        worst_case_delivery_times = self.calculate_robust_arrival_times(sequence, start_time, start_pos)
        
        max_delay_degree = 0.0
        for order_id, delivery_time in worst_case_delivery_times.items():
            delay_degree = self.calculate_order_delay_degree(order_id, delivery_time, stp_start_time)
            max_delay_degree = max(max_delay_degree, delay_degree)
        
        return max_delay_degree

    def generate_order_groups_for_stp(self, orders_in_stp: List[int], stp_time_str: str, actual_driver_start_pos: Tuple[float, float], actual_driver_start_time: float) -> List[OrderGroup]:
        if not orders_in_stp:
            return []
        
        hour, minute = map(int, stp_time_str.split(':'))
        stp_start_time_minutes = hour * 60 + minute
        delay_calc_start_time = max(actual_driver_start_time, stp_start_time_minutes)

        all_order_groups = []
        
        for starting_order in orders_in_stp:
            current_order_cluster = {starting_order}
            current_sequence = [f"pickup_{starting_order}", f"delivery_{starting_order}"]
            remaining_orders = set(orders_in_stp) - {starting_order}
            
            initial_og = OrderGroup([starting_order], current_sequence.copy(), starting_order)
            all_order_groups.append(initial_og)
            
            while remaining_orders and len(current_order_cluster) < self.max_orders_per_driver:
                order_matching_scores = []
                # d_bar: 当前序列的总距离
                d_bar = self.calculate_sequence_total_distance(current_sequence, actual_driver_start_pos)

                for order_id in remaining_orders:
                    # d_hat: 单独配送新订单的距离
                    d_hat = self.calculate_single_order_distance(order_id, actual_driver_start_pos)
                    # d_star: 使用最便宜插入法合并后的新序列距离
                    new_sequence_candidate, d_star = self.cheapest_insertion_single_order(
                        current_sequence, order_id, actual_driver_start_pos
                    )
                    # fit: 距离节约量
                    fit = d_bar + d_hat - d_star
                    order_matching_scores.append((order_id, fit, new_sequence_candidate))
                
                order_matching_scores.sort(key=lambda x: x[1], reverse=True)
                if not order_matching_scores or order_matching_scores[0][1] <= 0:
                    break
                
                best_order, best_fit, best_new_sequence = order_matching_scores[0]
                
                max_delay_degree = self.calculate_max_delay_degree_in_sequence(
                    best_new_sequence, actual_driver_start_time, delay_calc_start_time, actual_driver_start_pos
                )
                
                if max_delay_degree > self.delay_threshold:
                    # 如果延迟超限，则不再尝试将此订单加入，并从候选中移除
                    remaining_orders.remove(best_order)
                    continue

                current_order_cluster.add(best_order)
                current_sequence = best_new_sequence # 更新序列为插入后的最优序列
                remaining_orders.remove(best_order)
                new_og = OrderGroup(list(current_order_cluster), current_sequence.copy(), starting_order)
                all_order_groups.append(new_og)
        
        unique_order_groups = []
        seen_order_sets = set()
        for og in sorted(all_order_groups, key=lambda x: (len(x.orders), tuple(sorted(x.orders)))):
            orders_tuple = tuple(sorted(og.orders))
            if orders_tuple not in seen_order_sets:
                unique_order_groups.append(og)
                seen_order_sets.add(orders_tuple)
        
        return unique_order_groups


class Driver:
    """司机状态类"""
    def __init__(self, driver_id: int, start_pos: Tuple[float, float], start_time: float, capacity: int):
        self.driver_id = driver_id
        self.current_pos = start_pos
        self.current_time = start_time
        self.remaining_capacity = capacity
        self.assigned_orders = []
        self.total_cost = 0.0
        self.total_distance = 0.0
        
    def can_handle_order_group(self, order_group) -> bool:
        return self.remaining_capacity >= len(order_group)
    
    def assign_order_group(self, order_group, execution_cost: float, new_pos: Tuple[float, float], new_time: float, distance: float):
        self.assigned_orders.extend(order_group.orders)
        self.remaining_capacity -= len(order_group)
        self.total_cost += execution_cost
        self.total_distance += distance
        self.current_pos = new_pos
        self.current_time = new_time


class HybridOGAH:
    """订单组分配启发式算法 - 作为Gurobi模型的备用"""
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.num_drivers = food_delivery_optimizer.num_drivers
        self.current_driver_states = None

    def calculate_distance(self, coord1, coord2):
        return haversine(coord1[0], coord1[1], coord2[0], coord2[1])

    def calculate_sequence_execution_details(self, sequence, driver_start_pos, driver_start_time, stp_start_time_minutes):
        current_pos, current_time, total_distance, total_penalty = driver_start_pos, driver_start_time, 0.0, 0.0
        
        for node in sequence:
            if node.startswith("pickup_"):
                order_id = int(node.split("_")[1])
                next_pos = self.restaurant_coords[self.order_restaurants[order_id]]
            else:
                order_id = int(node.split("_")[1])
                next_pos = self.customer_coords[order_id]
            
            travel_dist = self.calculate_distance(current_pos, next_pos)
            travel_time = travel_dist / self.driver_speed
            total_distance += travel_dist
            current_time += travel_time
            current_pos = next_pos
            
            if node.startswith("delivery_"):
                target_time = stp_start_time_minutes + self.estimated_delivery_duration.get(order_id, 0)
                delay = max(current_time - target_time, 0)
                total_penalty += delay * self.penalty_cost_per_unit
        
        travel_cost = total_distance * self.travel_cost_per_unit
        return travel_cost, total_penalty, current_pos, current_time

    def initialize_drivers(self, stp_time_minutes: float, initial_states: List[Driver] = None) -> List[Driver]:
        drivers = []
        if initial_states:
            for prev_state in initial_states:
                start_time = max(prev_state.current_time, stp_time_minutes)
                driver = Driver(prev_state.driver_id, prev_state.current_pos, start_time, self.max_orders_per_driver)
                drivers.append(driver)
        else:
            for driver_id in range(self.num_drivers):
                start_pos = self.driver_start_coords.get(driver_id, list(self.driver_start_coords.values())[0])
                drivers.append(Driver(driver_id, start_pos, stp_time_minutes, self.max_orders_per_driver))
        return drivers

    def assign_order_groups_to_drivers(self, selected_order_groups: List[OrderGroup], stp_time_str: str, initial_driver_states: List[Driver] = None) -> Tuple[float, List[Driver], Dict]:
        hour, minute = map(int, stp_time_str.split(':'))
        stp_time_minutes = hour * 60 + minute
        drivers = self.initialize_drivers(stp_time_minutes, initial_driver_states)
        
        if not selected_order_groups:
            return 0.0, drivers, {'assignments': [], 'total_travel_cost': 0.0, 'total_penalty_cost': 0.0}

        return 0.0, drivers, {} # Fallback logic not fully implemented as Gurobi is primary


class GurobiSelectorAssigner:
    """使用Gurobi精确求解器替代OGSA和OGAH"""
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.num_drivers = food_delivery_optimizer.num_drivers
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = food_delivery_optimizer.customer_coords
        self.order_restaurants = food_delivery_optimizer.order_restaurants
        self.estimated_delivery_duration = food_delivery_optimizer.estimated_delivery_duration
        self.driver_start_coords = food_delivery_optimizer.driver_start_coords
        self.theta_t = getattr(food_delivery_optimizer, 'theta_t', 0.3)
        self.travel_time_deviation_ratio = getattr(food_delivery_optimizer, 'travel_time_deviation_ratio', 0.2)
        
    def get_node_pos(self, node_str: str) -> Tuple[float, float]:
        parts = node_str.split('_')
        order_id = int(parts[1])
        if parts[0] == "pickup":
            restaurant_id = self.order_restaurants[order_id]
            return self.restaurant_coords[restaurant_id]
        else:
            return self.customer_coords[order_id]

    def solve(self, order_groups: List[OrderGroup], orders_in_stp: List[int], stp_time_str: str, initial_driver_states: List[Driver] = None) -> Tuple[float, List[Driver], Dict]:
        hour, minute = map(int, stp_time_str.split(':'))
        stp_time_minutes = hour * 60 + minute
        
        ogah_fallback = HybridOGAH(self.optimizer)
        drivers = ogah_fallback.initialize_drivers(stp_time_minutes, initial_driver_states)

        if not order_groups or not orders_in_stp:
            return 0.0, drivers, {'assignments': [], 'total_travel_cost': 0.0, 'total_penalty_cost': 0.0}

        num_groups = len(order_groups)
        model = gp.Model(f"VRP_SP_STP_{stp_time_str}")
        model.setParam('OutputFlag', 0)
        
        y = model.addVars(num_groups, self.num_drivers, vtype=GRB.BINARY, name="y")
        
        total_cost = gp.LinExpr()
        total_travel_cost_expr = gp.LinExpr()
        total_penalty_cost_expr = gp.LinExpr()

        for k in range(self.num_drivers):
            driver = drivers[k]
            for i, og in enumerate(order_groups):
                # 使用 compute_rg2_robust_cost 计算成本
                # slot, slot_duration 在此上下文中不直接使用，可设为名义值
                travel_cost, penalty_cost, cost, _, _ = compute_rg2_robust_cost(
                    og.sequence, driver.current_pos, driver.current_time, stp_time_minutes,
                    self.restaurant_coords, self.customer_coords, self.order_restaurants,
                    self.driver_speed, self.theta_t, self.travel_time_deviation_ratio,
                    self.travel_cost_per_unit, self.penalty_cost_per_unit,
                    self.estimated_delivery_duration, list(self.driver_start_coords.values())[0]
                )
                total_cost += cost * y[i, k]
                total_travel_cost_expr += travel_cost * y[i, k]
                total_penalty_cost_expr += penalty_cost * y[i, k]
        
        model.setObjective(total_cost, GRB.MINIMIZE)

        for order_id in orders_in_stp:
            model.addConstr(gp.quicksum(y[i, k] for i, og in enumerate(order_groups) if order_id in og.orders for k in range(self.num_drivers)) == 1, f"cover_{order_id}")
        
        for k in range(self.num_drivers):
            model.addConstr(gp.quicksum(len(og.orders) * y[i, k] for i, og in enumerate(order_groups)) <= self.optimizer.max_orders_per_driver, f"capacity_{k}")
        
        model.optimize()

        if model.status != GRB.OPTIMAL:
            return float('inf'), drivers, {'assignments': [], 'message': 'Gurobi failed to find optimal solution'}

        final_drivers = copy.deepcopy(drivers)
        assignment_details = {'assignments': [], 'total_travel_cost': 0, 'total_penalty_cost': 0}
        
        for k in range(self.num_drivers):
            for i, og in enumerate(order_groups):
                if y[i, k].X > 0.5:
                    _, _, cost, final_pos, final_time = compute_rg2_robust_cost(
                        og.sequence, final_drivers[k].current_pos, final_drivers[k].current_time, stp_time_minutes,
                        self.restaurant_coords, self.customer_coords, self.order_restaurants,
                        self.driver_speed, self.theta_t, self.travel_time_deviation_ratio,
                        self.travel_cost_per_unit, self.penalty_cost_per_unit,
                        self.estimated_delivery_duration, list(self.driver_start_coords.values())[0]
                    )
                    final_drivers[k].assign_order_group(og, cost, final_pos, final_time, 0)
                    assignment_details['assignments'].append({'order_group': og, 'driver_id': k, 'cost': cost})
        
        assignment_details['total_travel_cost'] = total_travel_cost_expr.getValue()
        assignment_details['total_penalty_cost'] = total_penalty_cost_expr.getValue()
        
        return model.ObjVal, final_drivers, assignment_details

# =================================================================================
# == 代码从 Robust Hybrid.py 移植并适配 (结束)
# =================================================================================


class OCDAEvaluator:
    """OCDA算法评估器，用于评估定价方案的效果"""
    
    def __init__(self, debug_max_consumers=None):
        self.debug_max_consumers = debug_max_consumers
        self.setup_time_structure()
        self.load_base_data()
        self.prepare_solver_data()
        
    def setup_time_structure(self):
        """建立时间结构 - 严格按照论文设定"""
        # 11:00-15:00，每30分钟一个时间段，共8个时间段
        time_ranges = pd.date_range(start='2022-10-17 11:00:00', end='2022-10-17 15:00:00', freq='30min')
        self.time_periods = [f"{t.hour}:{t.minute:02d}-{(t + pd.Timedelta(minutes=30)).hour}:{(t + pd.Timedelta(minutes=30)).minute:02d}" 
                           for t in time_ranges[:-1]]
        
        # 每个时间段包含3个STP，每10分钟一个
        base_time = datetime(2022, 10, 17, 11, 0, 0)
        self.time_points = [(base_time + timedelta(minutes=10*i)).strftime('%H:%M') for i in range(24)]
        
        # 建立时间点到时间段的映射
        self.time_point_to_period = {}
        for i, time_point in enumerate(self.time_points):
            period_index = i // 3
            if period_index < len(self.time_periods):
                self.time_point_to_period[time_point] = self.time_periods[period_index]
            
    def prepare_solver_data(self):
        """准备求解器所需的数据结构"""
        # 创建一个模拟的优化器对象来存储所需参数
        self.mock_optimizer = SimpleNamespace()
        
        # 算法参数
        self.mock_optimizer.commission_rate = 0.18
        self.mock_optimizer.travel_cost_per_unit = 0.2  # 等同于 alpha
        self.mock_optimizer.penalty_cost_per_unit = 1.0   # 等同于 beta
        self.mock_optimizer.driver_speed = 20 / 60  # km/min，假设坐标单位为km
        self.mock_optimizer.max_orders_per_driver = 10  # U
        self.mock_optimizer.num_drivers = 30
        self.mock_optimizer.theta_t = 0.3
        self.mock_optimizer.travel_time_deviation_ratio = 0.2
        self.mock_optimizer.slot_duration = 30 # Not directly used in cost, but good for context

        # 空间和订单信息
        # 顾客ID(consumer_id)从1开始，映射到DataFrame索引
        self.mock_optimizer.customer_coords = {
            # 错误来源：df_preferences没有坐标信息，应该从orders_df获取
            # index + 1 对应 consumer_id
            index + 1: (row.recipient_lng_decimal, row.recipient_lat_decimal)
            for index, row in self.orders_df.iterrows()
        }
        
        # 餐厅坐标 (去重)
        restaurant_set = self.orders_df[['sender_lng_decimal', 'sender_lat_decimal']].drop_duplicates()
        self.mock_optimizer.restaurant_coords = {i: (row.sender_lng_decimal, row.sender_lat_decimal) for i, row in enumerate(restaurant_set.itertuples())}
        # 创建一个反向映射，便于查找餐厅ID
        restaurant_pos_to_id = {v: k for k, v in self.mock_optimizer.restaurant_coords.items()}

        # 每个订单属于哪个餐厅
        self.mock_optimizer.order_restaurants = {}
        for idx, row in self.orders_df.iterrows():
            consumer_id = idx + 1 # consumer_id = index + 1
            pos = (row.sender_lng_decimal, row.sender_lat_decimal)
            if pos in restaurant_pos_to_id:
                self.mock_optimizer.order_restaurants[consumer_id] = restaurant_pos_to_id[pos]
        
        # 订单价值和预期配送时间，键应为 consumer_id (index + 1)
        self.mock_optimizer.order_values = {
            idx + 1: val for idx, val in self.orders_df['price'].items()
        }
        self.mock_optimizer.estimated_delivery_duration = {
            idx + 1: val for idx, val in self.orders_df['estimated_delivery_time'].items()
        }


        # 司机起始位置
        grab_positions = self.orders_df[['grab_lng_decimal', 'grab_lat_decimal']].dropna()
        grab_positions = grab_positions[(grab_positions['grab_lng_decimal'] != 0) & (grab_positions['grab_lat_decimal'] != 0)]
        
        if len(grab_positions) < self.mock_optimizer.num_drivers:
            driver_pos_list = grab_positions.values.tolist()
        else:
            driver_pos_list = grab_positions.sample(n=self.mock_optimizer.num_drivers, random_state=42).values.tolist()
        
        self.mock_optimizer.driver_start_coords = {i: (pos[0], pos[1]) for i, pos in enumerate(driver_pos_list)}

        
    def load_base_data(self):
        """加载基础数据"""
        try:
            self.df_preferences = pd.read_csv('consumer_delivery_fee_preferences.csv')
            self.orders_df = pd.read_csv('meituan_orders_with_delivery_time.csv')
            
            # 计算时间段订单统计 (基于原始订单时间，作为基准)
            self.orders_df['platform_order_time'] = pd.to_datetime(self.orders_df['platform_order_time'])
            
            self.period_orders = {}
            time_ranges = pd.date_range(start='2022-10-17 11:00:00', end='2022-10-17 15:00:00', freq='30min')

            for i in range(len(self.time_periods)):
                period = self.time_periods[i]
                start_time = time_ranges[i]
                end_time = time_ranges[i+1]
                count = self.orders_df[(self.orders_df['platform_order_time'] >= start_time) & 
                                       (self.orders_df['platform_order_time'] < end_time)].shape[0]
                self.period_orders[period] = count
                
        except Exception as e:
            print(f"基础数据加载失败: {e}")
            raise
        
    def evaluate_pricing_scheme(self, period_price_map):
        """评估定价方案的效果 - 严格按照论文公式计算"""
        try:
            
            # 1. 根据新的定价方案计算消费者STP选择（论文中的CIIP求解）
            new_stp_choices = self.calculate_stp_choices(period_price_map)
            purchase_count = len([c for c in new_stp_choices if c['choice_type'] == 'purchase'])
            
            # 2. 如果没有消费者购买，返回极低利润
            if purchase_count == 0:
                print("警告：没有消费者购买，返回极低利润")
                return float('-inf'), 0, float('inf')
            
            
            # 3. 生成临时STP选择文件
            temp_stp_file = self.create_temp_stp_file(new_stp_choices)
            
            # 4. 调用新的混合算法 (HybridOGGM + Gurobi)
            cost_breakdown, revenue_breakdown = self.run_hybrid_solver(temp_stp_file)
            
            # 5. 计算总利润
            total_cost = cost_breakdown['total_cost']
            total_revenue = revenue_breakdown['total_revenue']
            total_profit = total_revenue - total_cost
            
            
            # 6. 清理临时文件
            self.cleanup_temp_files(temp_stp_file)
            
            return total_profit, total_revenue, total_cost
            
        except Exception as e:
            print(f"评估定价方案时出错: {e}")
            import traceback
            traceback.print_exc()
            return float('-inf'), 0, float('inf')
    
    def calculate_stp_choices(self, period_price_map):
        """根据新的定价方案计算消费者STP选择 - 严格按照论文CIIP实现"""
        
        # 构建时间点价格映射
        time_point_prices = {}
        for time_point, period in self.time_point_to_period.items():
            if period in period_price_map:
                time_point_prices[time_point] = period_price_map[period]
            else:
                time_point_prices[time_point] = None
        
        # 构建可用TPC集合 E'（论文中的定义）
        available_tpcs = []
        for time_point, price in time_point_prices.items():
            if price is not None:
                available_tpcs.append((time_point, price))
        available_tpcs.append(("不买", None))  # 添加不购买选项
        
        
        # 为每个消费者求解CIIP（按照论文附录D的Proposition 1）
        consumer_choices = []
        
        df_prefs_to_use = self.df_preferences
        if self.debug_max_consumers is not None:
            df_prefs_to_use = self.df_preferences.head(self.debug_max_consumers)
        
        for _, row in df_prefs_to_use.iterrows():
            consumer_id = row['consumer_id']
            preference_str = row['preferences']
            
            # 解析消费者偏好列表σ
            consumer_preferences = self.parse_consumer_preferences(preference_str)
            
            # 求解CIIP：找到偏好列表中排名最高的可用TPC
            optimal_choice, preference_rank = self.solve_ciip_for_consumer(
                consumer_preferences, available_tpcs)
            
            # 记录结果
            choice_type = 'purchase' if optimal_choice[0] != "不买" else 'no_purchase'
            
            consumer_choices.append({
                'consumer_id': consumer_id,
                'chosen_time': optimal_choice[0],
                'chosen_price': optimal_choice[1],
                'choice_type': choice_type,
                'preference_rank': preference_rank,
                'time_period': self.time_point_to_period.get(optimal_choice[0], None) if optimal_choice[0] != "不买" else None
            })
        
        purchase_choices = [c for c in consumer_choices if c['choice_type'] == 'purchase']
        
        return consumer_choices
    
    def parse_consumer_preferences(self, preference_str):
        """解析消费者偏好列表字符串"""
        preferences = []
        items = preference_str.split(', ')
        
        for item in items:
            if item == "不买":
                preferences.append(("不买", None))
            else:
                parts = item.split('-')
                time = parts[0]
                price_str = parts[1].replace('元', '')
                price = int(price_str)
                preferences.append((time, price))
        
        return preferences
    
    def solve_ciip_for_consumer(self, consumer_preferences, available_tpcs):
        """为单个消费者求解CIIP - 严格按照论文附录D的算法"""
        available_set = set(available_tpcs)
        
        # 按偏好顺序查找第一个可用的TPC（论文约束D.3和D.4）
        for rank, preference_tpc in enumerate(consumer_preferences):
            if preference_tpc in available_set:
                return preference_tpc, rank + 1
        
        # 如果没有找到匹配的偏好，选择不购买
        return ("不买", None), len(consumer_preferences)
    
    def create_temp_stp_file(self, stp_choices):
        """创建临时STP选择文件"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='_stp_choices.csv', delete=False)
        temp_filename = temp_file.name
        temp_file.close()
        
        stp_df = pd.DataFrame(stp_choices)
        stp_df.to_csv(temp_filename, index=False)
        
        return temp_filename
    
    def run_hybrid_solver(self, stp_file):
        """运行新的混合求解器 (HybridOGGM + GurobiSelectorAssigner)"""
        try:
            # 1. 准备数据
            stp_df = pd.read_csv(stp_file)
            purchase_df = stp_df[stp_df['choice_type'] == 'purchase']

            # 按STP对订单进行分组
            orders_by_stp = {}
            for _, row in purchase_df.iterrows():
                stp = self.time_point_to_period[row['chosen_time']]
                if stp not in orders_by_stp:
                    orders_by_stp[stp] = []
                orders_by_stp[stp].append(row['consumer_id'])

            # 2. 初始化求解器和司机状态
            oggm = HybridOGGM(self.mock_optimizer)
            gurobi_solver = GurobiSelectorAssigner(self.mock_optimizer)
            
            initial_driver_states = [
                Driver(
                    driver_id=i,
                    start_pos=self.mock_optimizer.driver_start_coords[i],
                    start_time=0,
                    capacity=self.mock_optimizer.max_orders_per_driver
                ) for i in range(self.mock_optimizer.num_drivers)
            ]
            
            # 3. 按STP顺序求解
            total_cost = 0
            total_travel_cost = 0
            total_penalty_cost = 0
            current_driver_states = initial_driver_states
            
            sorted_stps = sorted(list(orders_by_stp.keys()), key=lambda p: self.time_periods.index(p))
            
            for stp_period in sorted_stps:
                orders_in_this_stp = orders_by_stp[stp_period]
                
                # 确定STP的代表性时间点 (例如，该时间段的第一个10分钟点)
                representative_time_point = [k for k, v in self.time_point_to_period.items() if v == stp_period][0]
                
                # 使用第一个司机的状态作为代表性状态来生成订单组
                rep_driver_state = current_driver_states[0]
                
                # Step 3.1: OGGM生成订单组
                order_groups = oggm.generate_order_groups_for_stp(
                    orders_in_this_stp, 
                    representative_time_point,
                    rep_driver_state.current_pos,
                    rep_driver_state.current_time
                )
                
                # Step 3.2: Gurobi选择并分配
                stp_cost, final_drivers, assignment_details = gurobi_solver.solve(
                    order_groups,
                    orders_in_this_stp,
                    representative_time_point,
                    current_driver_states
                )
                
                if stp_cost == float('inf'): # Gurobi求解失败
                     print(f"警告: Gurobi在STP {stp_period}求解失败，成本设为无穷大")
                     total_cost = float('inf')
                     break

                # 更新成本和司机状态
                total_cost += stp_cost
                total_travel_cost += assignment_details.get('total_travel_cost', 0)
                total_penalty_cost += assignment_details.get('total_penalty_cost', 0)
                current_driver_states = final_drivers

            # 4. 构建成本和收益分解
            cost_breakdown = {
                'total_cost': total_cost,
                'total_travel_cost': total_travel_cost,
                'total_penalty_cost': total_penalty_cost,
                'cost_composition': {
                    'travel_percentage': (total_travel_cost / total_cost * 100) if total_cost > 0 else 0,
                    'penalty_percentage': (total_penalty_cost / total_cost * 100) if total_cost > 0 else 0
                } if total_cost < float('inf') else {}
            }
            
            revenue_breakdown = self.calculate_revenue_breakdown_paper_compliant(stp_file)
            
            return cost_breakdown, revenue_breakdown

        except gp.GurobiError as e:
            print(f"Gurobi错误: {e.message} (代码: {e.errno})")
            # 当Gurobi出错时（例如，没有许可证），返回极差的结果
            return {'total_cost': float('inf')}, {'total_revenue': 0}
        except Exception as e:
            print(f"混合求解器执行失败: {e}")
            import traceback
            traceback.print_exc()
            raise

    def calculate_revenue_breakdown_paper_compliant(self, stp_file):
        """计算收益分解"""
        
        stp_df = pd.read_csv(stp_file)
        purchase_df = stp_df[stp_df['choice_type'] == 'purchase']
        
        total_revenue = 0
        total_commission = 0
        total_delivery_fees = 0
        commission_rate = 0.18  # θ = 18%（论文参数）
        
        order_count = 0
        stp_revenues = {}
        
        for _, row in purchase_df.iterrows():
            consumer_id = row['consumer_id']
            chosen_price = row['chosen_price']
            chosen_time = row['chosen_time']
            
            # 获取订单价值v̄_i
            if consumer_id <= len(self.orders_df):
                order_row = self.orders_df.iloc[consumer_id - 1]
                order_value = order_row['price']
                
                # 严格按照论文公式(4)计算收益：R̃w = ∑(θv̄wi + p̂c)
                commission = order_value * commission_rate  # θv̄_i
                delivery_fee = chosen_price  # p̂_c
                order_revenue = commission + delivery_fee
                
                total_revenue += order_revenue
                total_commission += commission
                total_delivery_fees += delivery_fee
                order_count += 1
                
                # 按STP分组统计
                if chosen_time not in stp_revenues:
                    stp_revenues[chosen_time] = {
                        'revenue': 0,
                        'commission': 0,
                        'delivery_fees': 0,
                        'order_count': 0
                    }
                
                stp_revenues[chosen_time]['revenue'] += order_revenue
                stp_revenues[chosen_time]['commission'] += commission
                stp_revenues[chosen_time]['delivery_fees'] += delivery_fee
                stp_revenues[chosen_time]['order_count'] += 1
        
        revenue_composition = {}
        if total_revenue > 0:
            revenue_composition = {
                'commission_percentage': (total_commission / total_revenue * 100),
                'delivery_fee_percentage': (total_delivery_fees / total_revenue * 100),
                'average_order_value': total_commission / commission_rate / order_count if order_count > 0 else 0,
                'average_delivery_fee': total_delivery_fees / order_count if order_count > 0 else 0
            }
        
        
        return {
            'total_revenue': total_revenue,
            'total_commission': total_commission,
            'total_delivery_fees': total_delivery_fees,
            'order_count': order_count,
            'stp_breakdown': stp_revenues,
            'revenue_composition': revenue_composition
        }
    
    def cleanup_temp_files(self, *files):
        """清理临时文件"""
        for file in files:
            if file and os.path.exists(file):
                try:
                    os.unlink(file)
                except:
                    pass


class DestroyRepairOperators:
    """破坏算子和修复算子实现 - 严格按照论文5.1.2描述"""
    
    def __init__(self, debug_max_consumers=None):
        self.evaluator = OCDAEvaluator(debug_max_consumers=debug_max_consumers)
        self.price_set = [2, 3, 4, 5, 6, 7, 8, 9]  # 论文设定的价格集合
        
        # HALNS算法参数（严格按照Table 4）
        self.eta = 40          # η: 段内迭代数
        self.sigma_1 = 30      # σ̄₁: 新最佳解评分
        self.sigma_2 = 9       # σ̄₂: 更好解评分
        self.sigma_3 = 3       # σ̄₃: 被接受恶化解评分
        self.r = 0.1           # r: 反应因子
        self.alpha_cooling = 0.975  # ᾱ: 冷却率
        self.w_hat = 0.05      # ŵ: 恶化百分比(5%)
        self.delta_hat = 7     # δ̂: 可接受邻域大小
        
        # 定义破坏算子（严格按照论文5.1.2）
        self.destroy_operators = [
            ("random_removal", self.random_removal),
            ("maximum_deviation_removal", self.maximum_deviation_removal),
            ("random_pair_removal", self.random_pair_removal),
            ("max_min_removal", self.max_min_removal),
            ("differentiated_period_removal", self.differentiated_period_removal)
        ]
        
        # 定义修复算子（严格按照论文5.1.2）
        self.repair_operators = [
            ("greedy_assignment_repair", self.greedy_assignment_repair),
            ("admissible_neighborhood_repair", self.admissible_neighborhood_repair)
        ]
        
        # 初始化权重（按照论文5.1.3的自适应机制）
        self.destroy_weights = [1.0] * len(self.destroy_operators)
        self.repair_weights = [1.0] * len(self.repair_operators)
        
        # 当前段的统计信息（论文附录I）
        self.destroy_scores = [0.0] * len(self.destroy_operators)
        self.repair_scores = [0.0] * len(self.repair_operators)
        self.destroy_uses = [0] * len(self.destroy_operators)
        self.repair_uses = [0] * len(self.repair_operators)
        
        # 温度相关（论文附录I的模拟退火）
        self.temperature = None
        self.initial_temperature = None
        
        # 解记录
        self.best_solution = None
        self.best_profit = float('-inf')
        self.current_solution = None
        self.current_profit = float('-inf')
        
        
    # ===== 破坏算子（严格按照论文5.1.2）=====
    
    def random_removal(self, period_price_map, period_orders):
        """随机删除算子 - 随机选择一个时间段并移除其价格"""
        result = period_price_map.copy()
        selected_period = random.choice(list(result.keys()))
        del result[selected_period]
        return result, [selected_period]
    
    def maximum_deviation_removal(self, period_price_map, period_orders):
        """最大偏差删除算子 - 选择与平均订单数偏差最大的时间段"""
        result = period_price_map.copy()
        avg_orders = np.mean(list(period_orders.values()))
        max_deviation = 0
        max_dev_period = None
        
        for period, orders in period_orders.items():
            deviation = abs(orders - avg_orders)
            if deviation > max_deviation:
                max_deviation = deviation
                max_dev_period = period
        
        del result[max_dev_period]
        return result, [max_dev_period]
    
    def random_pair_removal(self, period_price_map, period_orders):
        """随机成对删除算子 - 随机选择一对时间段"""
        result = period_price_map.copy()
        periods = list(result.keys())
        if len(periods) < 2:
            return result, []
        
        selected_pair = random.sample(periods, 2)
        for period in selected_pair:
            del result[period]
        
        return result, selected_pair
    
    def max_min_removal(self, period_price_map, period_orders):
        """最大最小删除算子 - 选择订单量最大和最小的时间段"""
        result = period_price_map.copy()
        
        sorted_periods = sorted(period_orders.items(), key=lambda x: x[1])
        
        min_period = sorted_periods[0][0]   # 订单量最少
        max_period = sorted_periods[-1][0]  # 订单量最多
        
        del result[min_period]
        del result[max_period]
        
        return result, [min_period, max_period]
    
    def differentiated_period_removal(self, period_price_map, period_orders):
        """差异化时段删除算子 - 分别选择高峰和非高峰时段"""
        result = period_price_map.copy()
        
        avg_orders = np.mean(list(period_orders.values()))
        
        peak_periods = [period for period, count in period_orders.items() if count >= avg_orders]
        non_peak_periods = [period for period, count in period_orders.items() if count < avg_orders]
        
        if not peak_periods or not non_peak_periods:
            return result, []
        
        peak_period = random.choice(peak_periods)
        non_peak_period = random.choice(non_peak_periods)
        
        del result[peak_period]
        del result[non_peak_period]
        
        return result, [peak_period, non_peak_period]
    
    # ===== 修复算子（严格按照论文5.1.2）=====
    
    def calculate_optional_price_set(self, target_period, current_pricing, period_orders):
        """计算可选价格集合 - 按照论文5.1.2的方法"""
        optional_prices = set()
        periods = self.evaluator.time_periods
        target_idx = periods.index(target_period)
        
        # 第一个价格集合：位于前后时间段价格之间的价格点
        prev_price = None
        next_price = None
        
        for i in range(target_idx - 1, -1, -1):
            if periods[i] in current_pricing:
                prev_price = current_pricing[periods[i]]
                break
        
        for i in range(target_idx + 1, len(periods)):
            if periods[i] in current_pricing:
                next_price = current_pricing[periods[i]]
                break
        
        if prev_price is not None and next_price is not None:
            min_price = min(prev_price, next_price)
            max_price = max(prev_price, next_price)
            for price in self.price_set:
                if min_price <= price <= max_price:
                    optional_prices.add(price)
        
        # 第二个价格集合：基于订单量相对位置的价格点
        target_orders = period_orders[target_period]
        sorted_by_orders = sorted(period_orders.items(), key=lambda x: x[1])
        target_order_idx = next(i for i, (p, _) in enumerate(sorted_by_orders) if p == target_period)
        
        higher_period_price = None
        for i in range(target_order_idx + 1, len(sorted_by_orders)):
            period = sorted_by_orders[i][0]
            if period in current_pricing:
                higher_period_price = current_pricing[period]
                break
        
        lower_period_price = None
        for i in range(target_order_idx - 1, -1, -1):
            period = sorted_by_orders[i][0]
            if period in current_pricing:
                lower_period_price = current_pricing[period]
                break
        
        if higher_period_price is not None and lower_period_price is not None:
            min_price = min(higher_period_price, lower_period_price)
            max_price = max(higher_period_price, lower_period_price)
            for price in self.price_set:
                if min_price <= price <= max_price:
                    optional_prices.add(price)
        
        # 如果可选价格集合为空，使用所有价格
        if not optional_prices:
            optional_prices = set(self.price_set)
        
        return sorted(list(optional_prices))
    
    def greedy_assignment_repair(self, destroyed_solution, removed_periods, period_orders, max_candidates=5):
        """贪心分配修复算子 - 在可选价格集合中选择最佳价格"""
        candidate_solutions = []
        
        if len(removed_periods) == 1:
            period = removed_periods[0]
            optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
            
            price_profits = []
            for price in optional_prices:
                test_solution = destroyed_solution.copy()
                test_solution[period] = price
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    price_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            price_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = price_profits[:max_candidates]
            
        else:
            # 多个时间段的组合枚举
            period_optional_prices = {}
            for period in removed_periods:
                optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
                period_optional_prices[period] = optional_prices[:3]
            
            combinations = list(product(*[period_optional_prices[p] for p in removed_periods]))
            
            if len(combinations) > max_candidates * 3:
                combinations = random.sample(combinations, max_candidates * 3)
            
            combination_profits = []
            for combination in combinations:
                test_solution = destroyed_solution.copy()
                for j, period in enumerate(removed_periods):
                    test_solution[period] = combination[j]
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    combination_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            combination_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = combination_profits[:max_candidates]
        
        # 备选解
        if not candidate_solutions:
            backup_solution = destroyed_solution.copy()
            median_price = self.price_set[len(self.price_set) // 2]
            for period in removed_periods:
                backup_solution[period] = median_price
            
            try:
                backup_profit, _, _ = self.evaluator.evaluate_pricing_scheme(backup_solution)
                candidate_solutions = [(backup_solution, backup_profit)]
            except:
                candidate_solutions = [(backup_solution, float('-inf'))]
        
        return candidate_solutions
    
    def admissible_neighborhood_repair(self, destroyed_solution, removed_periods, period_orders, max_candidates=5):
        """可接受邻域修复算子 - 在可接受价格集合中随机选择"""
        candidate_solutions = []
        
        if len(removed_periods) == 1:
            period = removed_periods[0]
            optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
            
            if len(optional_prices) <= self.delta_hat:
                admissible_prices = optional_prices
            else:
                admissible_prices = random.sample(optional_prices, self.delta_hat)
            
            price_profits = []
            for price in admissible_prices:
                test_solution = destroyed_solution.copy()
                test_solution[period] = price
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    price_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            price_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = price_profits[:max_candidates]
            
        else:
            period_optional_prices = {}
            for period in removed_periods:
                optional_prices = self.calculate_optional_price_set(period, destroyed_solution, period_orders)
                if len(optional_prices) <= 3:
                    period_optional_prices[period] = optional_prices
                else:
                    period_optional_prices[period] = random.sample(optional_prices, 3)
            
            combinations = list(product(*[period_optional_prices[p] for p in removed_periods]))
            
            if len(combinations) > self.delta_hat:
                selected_combinations = random.sample(combinations, self.delta_hat)
            else:
                selected_combinations = combinations
            
            combination_profits = []
            for combination in selected_combinations:
                test_solution = destroyed_solution.copy()
                for j, period in enumerate(removed_periods):
                    test_solution[period] = combination[j]
                
                try:
                    profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(test_solution)
                    combination_profits.append((test_solution, profit))
                except Exception as e:
                    continue
            
            combination_profits.sort(key=lambda x: x[1], reverse=True)
            candidate_solutions = combination_profits[:max_candidates]
        
        if not candidate_solutions:
            return self.greedy_assignment_repair(destroyed_solution, removed_periods, period_orders, max_candidates)
        
        return candidate_solutions
    
    # ===== HALNS算法核心方法（严格按照论文5.1.3和附录I）=====
    
    def roulette_wheel_selection(self, weights):
        """轮盘赌选择 - 按照论文5.1.3的自适应机制"""
        total_weight = sum(weights)
        if total_weight == 0:
            return random.randint(0, len(weights) - 1)
        
        r = random.uniform(0, total_weight)
        cumulative = 0
        for i, weight in enumerate(weights):
            cumulative += weight
            if r <= cumulative:
                return i
        return len(weights) - 1

    def initialize_temperature(self, initial_solution, initial_profit):
        """初始化温度 - 严格按照论文附录I的描述"""
        worse_profit = initial_profit * (1 - self.w_hat)
        delta = abs(initial_profit - worse_profit)
        
        if delta > 0:
            # T_start设置使得ŵ%恶化的解以40%概率被接受
            self.initial_temperature = delta / abs(math.log(0.4))
        else:
            self.initial_temperature = 1000
        
        self.temperature = self.initial_temperature

    def accept_solution(self, new_profit, current_profit):
        """模拟退火接受准则 - 严格按照论文附录I"""
        if new_profit >= current_profit:
            return True
        
        delta = current_profit - new_profit
        probability = math.exp(-delta / self.temperature)
        return random.random() < probability

    def update_scores(self, destroy_idx, repair_idx, score_type):
        """更新算子评分 - 按照论文附录I的评分规则"""
        score_values = {
            'best': self.sigma_1,     # σ̄₁ = 30
            'better': self.sigma_2,   # σ̄₂ = 9
            'accepted': self.sigma_3  # σ̄₃ = 3
        }
        
        score = score_values.get(score_type, 0)
        self.destroy_scores[destroy_idx] += score
        self.repair_scores[repair_idx] += score

    def update_weights(self):
        """更新权重 - 严格按照论文附录I公式(I.1)"""
        # ω_{i,j+1} = (1-r)ω_{ij} + r(π_i/θ̄_{ij}) if θ̄_{ij} ≠ 0
        
        for i in range(len(self.destroy_operators)):
            if self.destroy_uses[i] > 0:
                avg_score = self.destroy_scores[i] / self.destroy_uses[i]
                self.destroy_weights[i] = (1 - self.r) * self.destroy_weights[i] + self.r * avg_score
        
        for i in range(len(self.repair_operators)):
            if self.repair_uses[i] > 0:
                avg_score = self.repair_scores[i] / self.repair_uses[i]
                self.repair_weights[i] = (1 - self.r) * self.repair_weights[i] + self.r * avg_score

    def reset_segment_stats(self):
        """重置段统计信息"""
        self.destroy_scores = [0.0] * len(self.destroy_operators)
        self.repair_scores = [0.0] * len(self.repair_operators)
        self.destroy_uses = [0] * len(self.destroy_operators)
        self.repair_uses = [0] * len(self.repair_operators)

    def run_halns(self, max_iterations=200):
        """运行HALNS算法 - 严格按照论文Algorithm 1和附录I"""
        
        start_time = time.time()
        
        # Step 1: 生成初始解（论文Algorithm 2）
        initial_solution = self.generate_initial_pricing_scheme()
        

        try:
            initial_profit, initial_revenue, initial_cost = self.evaluator.evaluate_pricing_scheme(initial_solution)

        except Exception as e:
            print(f"初始解评估失败: {e}")
            return None, None
        
        # Step 3: 初始化（论文Algorithm 1 Step 2）
        self.current_solution = initial_solution.copy()
        self.current_profit = initial_profit
        self.best_solution = initial_solution.copy()
        self.best_profit = initial_profit
        
        # Step 4: 初始化温度（论文附录I）
        self.initialize_temperature(initial_solution, initial_profit)
        
        period_orders = self.evaluator.period_orders
        
        # Step 5: 主循环（论文Algorithm 1 Step 3-27）
        iteration = 0
        accepted_count = 0
        improved_count = 0
        best_found_iteration = 0
        
        
        while iteration < max_iterations:
            iteration += 1
            
            # 段管理（论文Algorithm 1 Step 23-25）
            if (iteration - 1) % self.eta == 0:
                if iteration > 1:
                    self.update_weights()

                
                self.reset_segment_stats()
            

            
            # Step 5: 选择算子（论文Algorithm 1 Step 5）
            destroy_idx = self.roulette_wheel_selection(self.destroy_weights)
            repair_idx = self.roulette_wheel_selection(self.repair_weights)
            
            destroy_name, destroy_op = self.destroy_operators[destroy_idx]
            repair_name, repair_op = self.repair_operators[repair_idx]
            

            
            # Step 8: 更新使用次数
            self.destroy_uses[destroy_idx] += 1
            self.repair_uses[repair_idx] += 1
            
            try:
                # Step 6: 应用破坏和修复算子（论文Algorithm 1 Step 6）
                destroyed_solution, removed_periods = destroy_op(self.current_solution, period_orders)
                
                candidate_solutions = repair_op(destroyed_solution, removed_periods, period_orders)
                
                if not candidate_solutions:
                    continue
                
                # Step 7: 评估候选解并选择最佳的
                best_candidate_solution, best_candidate_profit = max(candidate_solutions, key=lambda x: x[1])
                
                # Step 9-22: 接受准则和评分更新
                score_type = None
                accept = False
                
                if best_candidate_profit > self.best_profit:
                    # 找到新的最佳解（论文Algorithm 1 Step 11-14）
                    self.best_solution = best_candidate_solution.copy()
                    self.best_profit = best_candidate_profit
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'best'
                    accept = True
                    best_found_iteration = iteration
                    improved_count += 1
                    
                elif best_candidate_profit > self.current_profit:
                    # 找到更好的解（论文Algorithm 1 Step 9-10）
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'better'
                    accept = True
                    improved_count += 1
                    
                elif self.accept_solution(best_candidate_profit, self.current_profit):
                    # 接受恶化解（论文Algorithm 1 Step 18-20）
                    self.current_solution = best_candidate_solution.copy()
                    self.current_profit = best_candidate_profit
                    score_type = 'accepted'
                    accept = True
                
                if accept:
                    accepted_count += 1
                
                # 更新评分（论文Algorithm 1 Step 13, 15, 20）
                if score_type:
                    self.update_scores(destroy_idx, repair_idx, score_type)
                
            except Exception as e:
                continue
            
            # Step 26: 更新温度（论文Algorithm 1）
            self.temperature *= self.alpha_cooling
            
            # 进度报告
            if iteration % 20 == 0:
                elapsed = time.time() - start_time
        
        # Step 28: 返回最佳解（论文Algorithm 1）
        total_time = time.time() - start_time
        
        print("\n" + "="*80)
        print("HALNS算法完成（严格按照论文实现）")
        print("="*80)
        print(f"总迭代数: {max_iterations}")
        print(f"总运行时间: {total_time:.1f}秒")
        print(f"平均每次迭代: {total_time/max_iterations:.2f}秒")
        print(f"总接受数: {accepted_count}")
        print(f"总改善数: {improved_count}")
        print(f"接受率: {accepted_count/max_iterations*100:.1f}%")
        print(f"改善率: {improved_count/max_iterations*100:.1f}%")
        print(f"最后改善迭代: {best_found_iteration}")
        
        print(f"\n最佳解:")
        for period, price in self.best_solution.items():
            orders = period_orders.get(period, 0)
            print(f"  {period}: {price}元 (订单量: {orders})")
        
        # 最终详细评估
        print(f"\n最佳解详细性能分析:")
        try:
            final_profit, final_revenue, final_cost = self.evaluator.evaluate_pricing_scheme(self.best_solution)
            
            print(f"\n  === 最终结果（严格按照论文公式计算）===")
            print(f"  总利润: {final_profit:.2f} RMB")
            print(f"  总收益: {final_revenue:.2f} RMB")
            print(f"  总成本: {final_cost:.2f} RMB")
            print(f"  利润率: {(final_profit / final_revenue * 100):.2f}%")
            
            # 与初始解比较
            profit_improvement = final_profit - initial_profit
            print(f"\n  === 与初始解比较 ===")
            print(f"  利润改善: {profit_improvement:+.2f} RMB")
            if initial_profit != 0:
                print(f"  利润改善率: {profit_improvement/abs(initial_profit)*100:+.2f}%")
                
        except Exception as e:
            print(f"最终详细评估失败: {e}")       
        
        return self.best_solution, self.best_profit
 
    # ===== 初始解生成方法（论文Algorithm 2）=====
    
    def generate_initial_pricing_scheme(self):
        """生成初始定价方案 - 严格按照论文Algorithm 2"""
        print("生成初始定价方案（论文Algorithm 2）...")
        
        # 生成5种候选方案并选择最佳的
        candidate_schemes = []
        
        # 方案1：峰值定价（论文Algorithm 2的主要思路）
        candidate_schemes.append(("峰值定价", self.generate_peak_pricing_scheme()))
        
        # 方案2-5：基于高峰/非高峰时段的四种组合
        peak_non_peak_schemes = self.generate_peak_non_peak_schemes()
        for i, scheme in enumerate(peak_non_peak_schemes):
            candidate_schemes.append((f"高峰非高峰组合{i+1}", scheme))
        
        # 评估所有候选方案
        best_scheme = None
        best_profit = float('-inf')
        best_name = ""

        print("正在评估候选初始方案...")
        for name, scheme in candidate_schemes:
            try:
                profit, revenue, cost = self.evaluator.evaluate_pricing_scheme(scheme)
                print(f"  {name}: 利润={profit:.2f}, 收益={revenue:.2f}, 成本={cost:.2f}")
                
                if profit > best_profit:
                    best_profit = profit
                    best_scheme = scheme.copy()
                    best_name = name
                    
            except Exception as e:
                print(f"  {name}: 评估失败 - {e}")
                continue

        if best_scheme is not None:
            print(f"选择最佳初始方案: {best_name} (利润: {best_profit:.2f})")
        else:
            print("所有候选方案评估失败，使用默认峰值定价方案")
            return self.generate_peak_pricing_scheme()

        return best_scheme

    def generate_peak_pricing_scheme(self):
        """生成峰值定价方案 - 按照论文Algorithm 2的思路"""
        period_orders = self.evaluator.period_orders
        sorted_periods = sorted(period_orders.items(), key=lambda x: x[1], reverse=True)
        
        initial_pricing = {}
        price_set_desc = sorted(self.price_set, reverse=True)
        
        for i, (period, _) in enumerate(sorted_periods):
            if i < len(price_set_desc):
                initial_pricing[period] = price_set_desc[i]
            else:
                initial_pricing[period] = price_set_desc[-1]
        
        return initial_pricing

    def generate_peak_non_peak_schemes(self):
        """生成基于高峰/非高峰时段的四种定价方案 - 按照论文Algorithm 2扩展"""
        period_orders = self.evaluator.period_orders
        
        # 计算平均订单数量（论文Algorithm 2的ξ̄）
        avg_orders = np.mean(list(period_orders.values()))
        
        # 划分高峰和非高峰时间段（论文Algorithm 2的T̂₁和T̂₂）
        peak_periods = [period for period, count in period_orders.items() if count >= avg_orders]
        non_peak_periods = [period for period, count in period_orders.items() if count < avg_orders]
        
        # 确保所有时间段都被覆盖
        all_periods = set(period_orders.keys())
        covered_periods = set(peak_periods) | set(non_peak_periods)
        if all_periods != covered_periods:
            for uncovered in (all_periods - covered_periods):
                non_peak_periods.append(uncovered)
        
        # 计算价格选项（论文Algorithm 2的q₁, q₂, q₃, q₄）
        price_set_sorted = sorted(self.price_set)
        delta = 1/4  # 论文Algorithm 2的δ
        
        q1_idx = int(delta * len(price_set_sorted))
        q2_idx = q1_idx + 1
        q3_idx = int(3 * delta * len(price_set_sorted))
        q4_idx = q3_idx + 1
        
        # 非高峰时间段的价格选项
        non_peak_price_options = [
            price_set_sorted[min(q1_idx, len(price_set_sorted)-1)], 
            price_set_sorted[min(q2_idx, len(price_set_sorted)-1)]
        ]
        
        # 高峰时间段的价格选项
        peak_price_options = [
            price_set_sorted[min(q3_idx, len(price_set_sorted)-1)], 
            price_set_sorted[min(q4_idx, len(price_set_sorted)-1)]
        ]
        
        # 生成四种组合方案
        schemes = []
        for peak_price in peak_price_options:
            for non_peak_price in non_peak_price_options:
                scheme = {}
                
                for period in peak_periods:
                    scheme[period] = peak_price
                
                for period in non_peak_periods:
                    scheme[period] = non_peak_price
                
                schemes.append(scheme)
        
        return schemes


if __name__ == "__main__":
    print("开始运行HALNS算法（严格按照论文实现）...")
    
    # 设置随机种子确保结果可重现
    random.seed(42)
    np.random.seed(42)
    
    try:
        # 创建破坏修复算子实例
        operators = DestroyRepairOperators(debug_max_consumers=50)
        
        # 运行完整HALNS算法
        max_iterations = 200  # 论文设定的迭代数
        best_solution, best_profit = operators.run_halns(max_iterations=max_iterations)
        
        if best_solution is not None:
            print(f"\n✓ HALNS算法完成！最佳利润: {best_profit:.2f}")
            print("\n最终解验证:")
            for period, price in best_solution.items():
                orders = operators.evaluator.period_orders.get(period, 0)
                print(f"  {period}: {price}元 (订单量: {orders})")
        else:
            print("\n✗ HALNS算法执行失败")
        
        print("\n✓ 程序执行完成！")
        print("严格按照论文Section 5.1.3和Appendix I实现的HALNS算法")
        
    except Exception as e:
        print(f"程序执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()